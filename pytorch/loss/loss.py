import torch.nn as nn
from .yolov5_loss import *
from .mobilenetv3_loss import *

def build_mobilenetV3_Loss(engine):
    engine.criterion = CombinedLoss(engine.config)

def build_yolov5_Loss(engine):
    engine.criterion = ComputeLoss(engine.config, engine.model)

def build_loss(engine):
    if engine.config["Global"]["update_mode"]:
        build_mobilenetV3_Loss(engine)
    else:
        build_yolov5_Loss(engine)
