import torch
import torch.nn as nn

class TripletMarginLoss(nn.Module):
    def __init__(self,
                 margin=0.6,
                 normalize_feature=True,
                 reduction="mean",
                 add_absolute=True,
                 absolute_loss_weight=1.0,
                 ap_value=0.8,
                 an_value=0.2):
        super(TripletMargin<PERSON>oss, self).__init__()
        self.margin = margin
        self.ranking_loss = nn.MarginRankingLoss(margin=margin, reduction=reduction)
        self.normalize_feature = normalize_feature
        self.add_absolute = add_absolute
        self.ap_value = ap_value
        self.an_value = an_value
        self.absolute_loss_weight = absolute_loss_weight

    def forward(self, inputs, target):
        if self.normalize_feature:
            inputs = torch.divide(inputs, torch.norm(inputs, p=2, dim=-1, keepdim=True))
        bs = inputs.shape[0]
        dist = torch.matmul(inputs, inputs.t())
        is_pos = target.expand((bs, bs))==(target.expand((bs, bs)).t())
        is_neg = target.expand((bs, bs))!=(target.expand((bs, bs)).t())
        dist_ap_ = dist * is_pos
        dist_an_ = dist * is_neg
        dist_ap_[~is_pos] = 1
        dist_an_[~is_neg] = -1
        dist_ap = torch.min(dist_ap_, dim=1).values
        dist_an = torch.max(dist_an_, dim=1).values
        y = torch.ones_like(dist_an)
        loss = self.ranking_loss(dist_ap, dist_an, y)
        if self.add_absolute:
            absolut_loss_ap = self.ap_value - dist_ap
            absolut_loss_ap = torch.where(absolut_loss_ap > 0,
                                           absolut_loss_ap,
                                           torch.zeros_like(absolut_loss_ap))
            absolut_loss_an = dist_an - self.an_value
            absolut_loss_an = torch.where(absolut_loss_an > 0,
                                           absolut_loss_an,
                                           torch.zeros_like(absolut_loss_an))
            loss = (absolut_loss_an.mean() + absolut_loss_ap.mean()
                    ) * self.absolute_loss_weight + loss.mean()
        return loss


class CombinedLoss():
    def __init__(self, config):
        self.config = config
        self.CE = nn.CrossEntropyLoss()
        self.TripletMarginLoss = TripletMarginLoss()

    def __call__(self, predictions, targets):  # predictions, targets
        if self.config['Global']['eval_mode'] == 'retrieval':
            tripletloss = self.TripletMarginLoss(predictions[0], targets)
            celoss = self.CE(predictions[1], targets)
            loss = tripletloss + celoss
            loss_item = {"tripletloss": round(tripletloss.detach().cpu().item(), 4),
                         "celoss": round(celoss.detach().cpu().item(), 4)}
        else:
            celoss = self.CE(predictions, targets)
            loss = celoss
            loss_item = {"celoss": round(celoss.detach().cpu().item(), 4)}
        return loss, loss_item

