import torch.optim as optim
from torch.optim import lr_scheduler
from pytorch.utils.general import one_cycle
import torch.nn as nn


def build_optimizer(engine):
    batch_size = engine.config['Global']['batch_size']
    if engine.config["Global"]["update_mode"]:
        # optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9, nesterov=True)
        engine.optimizer = optim.SGD(engine.model.parameters(), lr=0.01, momentum=0.9)
        engine.scheduler = lr_scheduler.StepLR(engine.optimizer, step_size=7, gamma=0.1)
    else:
        nbs = 64  # nominal batch size
        accumulate = max(round(nbs / batch_size), 1)  # accumulate loss before optimizing
        weight_decay = 0.0005 * batch_size * accumulate / nbs  # scale weight_decay
        g0, g1, g2 = [], [], []  # optimizer parameter groups
        for v in engine.model.modules():
            if hasattr(v, 'bias') and isinstance(v.bias, nn.Parameter):  # bias
                g2.append(v.bias)
            if isinstance(v, nn.BatchNorm2d):  # weight (no decay)
                g0.append(v.weight)
            elif hasattr(v, 'weight') and isinstance(v.weight, nn.Parameter):  # weight (with decay)
                g1.append(v.weight)
        engine.optimizer = optim.SGD(g0, lr=0.01, momentum=0.937, nesterov=True)
        engine.optimizer.add_param_group({'params': g1, 'weight_decay': weight_decay})  # add g1 with weight_decay
        engine.optimizer.add_param_group({'params': g2})  # add g2 (biases)
        del g0, g1, g2
        lf = one_cycle(1, 0.1, engine.config['Global']['epochs'])
        engine.scheduler = lr_scheduler.LambdaLR(engine.optimizer, lr_lambda=lf)