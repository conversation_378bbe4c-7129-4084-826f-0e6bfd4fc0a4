import sys
from .model_list import CLASSIFICATION_MODELS_TAIHO, DETECTION_MODELS_TAIHO

EVAL_MODE = {0: 'retrieval', 1: 'classification' }
PLATFORM = {0:'gaotong', 1:'qingwei', 2:'qingwei'}

class Config():
    def __init__(self,
                 # Global
                 epochs=100,                      #  通过得分设置3个档位：20，60，150 只能通过config.yaml配置
                 batch_size=64,                  # 64
                 train_size= None,
                 val_size=1000,
                 sample_ratio=3,
                 eval_mode=1,                     # 过config.yaml配置覆盖该参数
                 profiler_options = None,
                 # Preprocess
                 image_size=[64, 64],             # 应用程序（autotrain.py）会重写覆盖该参数
                 retain_size_info=False,          # 应用程序（autotrain.py）会重写覆盖该参数
                 normalization=0,
                 pins_prob=0.5,
                 bgaug_prob=0.5,
                 claug_prob=0.2,
                 rt_fill= [0, 0, 0],
                 au_mix_prob=0.2,
                 # Arch
                 update_mode=0,  # 应用程序（autotrain.py）会重写覆盖该参数
                 claiffication_model_id = 0,                   # 只能通过config.yaml配置
                 detection_model_id = 0,                     # 只能通过config.yaml配置
                 platform=0,                      # 应用程序（autotrain.py）会重写覆盖该参数
                 feature_from='features',  # 'backbone' or 'features'    # 只能通过config.yaml配置
                 feature_dim=128,  # 128 or 512   # 只能通过config.yaml配置
                 class_num=None,                  # 应用程序（autotrain.py）会重写覆盖该参数
                 center=5,     # 履带杂粮机5, 整瓶10    # 只能通过config.yaml配置
                 fp_16 = True,
                 # Yolo_Augment
                 BG_HSV_Transform = True,
                 random_perspective = False,
                 random_perspective_degrees =  0.0,
                 random_perspective_translate =  0.0,
                 random_perspective_scale =  0.0,
                 random_perspective_shear =  0.0,
                 random_perspective_perspective =  0.0,
                 ioa = 0.1,
                 instance_lower_blue = [100, 100, 100],
                 instance_upper_blue = [140, 255, 255],
                 albumentations = False,
                 augment_hsv = False,
                 augment_hsv_h = 0.0,
                 augment_hsv_s = 0.0,
                 augment_hsv_v = 0.0,
                 flip = True,
                 background_img_Num = 350,
                 ):
        
        # Global
        self.epochs = epochs  # epochs 调试用
        self.eval_mode = EVAL_MODE[eval_mode]
        self.train_size = train_size if train_size!=None else 27000 if update_mode else 9000
        self.val_size = val_size
        self.sample_ratio = sample_ratio
        # preprocess
        self.image_size = image_size
        self.when_greater_do = retain_size_info
        self.normalization = normalization
        self.back_ground_lower_hsv = instance_lower_blue
        self.back_ground_upper_hsv = instance_upper_blue
        self.paste_instance_prob = pins_prob
        self.back_ground_aug_prob = bgaug_prob
        self.hsv_aug_prob = claug_prob
        self.rot_fill = rt_fill
        self.aug_mix_prob = au_mix_prob
        self.batch_size = batch_size 
        # backbone
        self.backbone = None
        if update_mode:
            self.backbone = CLASSIFICATION_MODELS_TAIHO[claiffication_model_id] # update_mode=1
        else:
            self.backbone = DETECTION_MODELS_TAIHO[detection_model_id]  # update_mode=0
        self.platform = PLATFORM[platform]
        self.update_mode = update_mode
        self.feature_from = feature_from  # 'backbone' or 'features'
        self.feature_dim = feature_dim  # 128 or 512
        self.class_num = class_num
        # softtriple loss
        self.tau = 0.
        self.margin = 0.1
        self.center_for_per_class = center
        # retrieve
        # preprocess normalization
        self.mean = [0.485, 0.456, 0.406] if normalization else [0., 0., 0.]
        self.std = [0.229, 0.224, 0.225] if normalization else [1., 1., 1.]
        self.fp_16 = fp_16

        self.BG_HSV_Transform = BG_HSV_Transform
        self.random_perspective = random_perspective
        self.random_perspective_degrees = random_perspective_degrees
        self.random_perspective_translate = random_perspective_translate
        self.random_perspective_scale = random_perspective_scale
        self.random_perspective_shear = random_perspective_shear
        self.random_perspective_perspective = random_perspective_perspective
        self.ioa = ioa
        self.instance_lower_blue = instance_lower_blue
        self.instance_upper_blue = instance_upper_blue
        self.albumentations = albumentations
        self.augment_hsv = augment_hsv
        self.augment_hsv_h = augment_hsv_h
        self.augment_hsv_s = augment_hsv_s
        self.augment_hsv_v = augment_hsv_v
        self.flip = flip
        self.background_img_Num = background_img_Num

        Global_dict = {
            'pretrained_model': None,
            'output_dir': "",
            'project_root': "",
            'start_eval': 0,
            'eval_interval': 1,
            'epochs': self.epochs,
            'eval_mode': self.eval_mode,
            'retrieval_feature_from': self.feature_from,
            'image_size': self.image_size,  # w,h
            'batch_size': self.batch_size,
            'platform':self.platform,
            'material_name': "",
            'cls_name': {},
            'val_path': "",
            'fp_16': self.fp_16,
            'model_dir': "",
            'update_mode': self.update_mode
        }

        Arch_dict = {
            'Backbone': {
                'name': self.backbone,  # supported: 0, 1
                'class_expand': self.feature_dim},
            'Head': {'class_num': self.class_num},
        }

        YOLOv5Loss_dict = {
            'cls_pw': 1.0,
            'obj_pw': 1.0,
            'label_smoothing': 0.0,
            'box': 0.05,
            'obj': 1.0,
            'cls': 0.5,
            'anchor_t': 4.0,
            'iou_t': 0.20
        }

        # classification ops list
        Train_transform_ops_list = [
            {
                'DecodeImage': {
                    'to_np' : True,
                    'to_rgb': True,
                    'channel_first': False}
            },
            {
                'RandFlipImage': {
                    'flip_code': -1
                }
            },
            {
                'PasteInstance': {
                    'image_root': "",
                    'cls_label_path': "",
                    'prob': self.paste_instance_prob,
                    'sample_num': 2,
                    'bg_lower_hsv': self.back_ground_lower_hsv,
                    'bg_upper_hsv': self.back_ground_upper_hsv,
                    'fg_overlap_ratio_upperLimit': 0.05,
                    'instance_area_lowLimit': 30
                }
            },
            {
                'ObjectBGAug': {
                    'prob': self.back_ground_aug_prob,
                    'bg_RGB': [5, 5, 5],
                    'bg_HSV': [0.015, 0.1, 0.1],
                    'lowerHSV_thresh_bg': self.back_ground_lower_hsv,
                    'upperHSV_thresh_bg': self.back_ground_upper_hsv
                }
            },
            # {
            #     'ColorJitter': {
            #         'prob': self.hsv_aug_prob,
            #         'brightness': 0.05,
            #         'contrast': 0.02,
            #         'saturation': 0.02
            #     }
            # },
            {
                'AugMix': {
                    'prob': self.aug_mix_prob,
                    'proportion': 0.25
                }
            },
            {
                'RandomRotation': {
                    'prob': 0.2,
                    'degrees': 90,
                    'fill': self.rot_fill,  # 填充黑背景
                    'expand': True
                }
            },
            {
                'ResizeProportionImage': {
                    'size': self.image_size,
                    'return_numpy': False,
                    'interpolation': 'bilinear',
                    'backend': 'cv2',
                    'when_greater_do': self.when_greater_do  # 默认为False, 屏端可配置
                }
            },
            {
                'Padv3': {
                    'size': self.image_size,
                    'pad_mode': 1,
                    'fill_value': [0, 0, 0]
                }
            },
            {
                'NormalizeImage': {
                    'scale': '1.0 / 255.0',
                    'mean': self.mean,
                    'std': self.std,
                    'order': 'hwc'
                }
            },
            {
                'ToCHWImage':{}
            }
        ]

        Eval_transform_ops_list = [
            {
                'DecodeImage': {
                    'to_rgb': True,
                    'channel_first': False
                }
            },
            {
                'ResizeProportionImage': {
                    'size': self.image_size,
                    'return_numpy': False,
                    'interpolation': 'bilinear',
                    'backend': 'cv2',
                    'when_greater_do': self.when_greater_do  # 默认为False, 屏端可配置
                }
            },
            {
                'Padv3': {
                    'size': self.image_size,
                    'pad_mode': 1,
                    'fill_value': [0, 0, 0]
                }
            },
            {
                'NormalizeImage': {
                    'scale': '1.0 / 255.0',
                    'mean': self.mean,
                    'std': self.std,
                    'order': 'hwc'
                }
            },
            {
                'ToCHWImage': {}
            }
        ]

        Train_dict = {
            'dataset': {
                'name': 'ImageNetDataset',
                'image_root': "",
                'cls_label_path': "",
                'transform_ops': Train_transform_ops_list,
                'background':"",
                'train_size': self.train_size,
                'sample_ratio': self.sample_ratio
            },
        }

        Val_dict = {
            'dataset': {
                'name': 'ImageNetDataset',
                'image_root': "",
                'cls_label_path': "",
                'relabel': False,
                'transform_ops': Eval_transform_ops_list,
                'background':"",
                'val_size': self.val_size,
                'sample_ratio': self.sample_ratio
            },
        }

        Yolo_Augment={
            "BG_HSV_Transform": self.BG_HSV_Transform,
            "bg_lower_hsv": self.back_ground_lower_hsv,
            "bg_upper_hsv": self.back_ground_upper_hsv,

            "background_img_Num":self.background_img_Num,

            "random_perspective": self.random_perspective,
            "random_perspective_degrees":self.random_perspective_degrees,
            "random_perspective_translate":self.random_perspective_translate,
            "random_perspective_scale":self.random_perspective_scale,
            "random_perspective_shear":self.random_perspective_shear,
            "random_perspective_perspective":self.random_perspective_perspective,

            "ioa":self.ioa,
            "instance_lower_blue": self.instance_lower_blue,
            "instance_upper_blue": self.instance_upper_blue,

            "albumentations":self.albumentations,

            "augment_hsv":self.augment_hsv,
            "augment_hsv_h":self.augment_hsv_h,
            "augment_hsv_s":self.augment_hsv_s,
            "augment_hsv_v":self.augment_hsv_v,

            "flip":self.flip,
        }

        if 'MobileNet' in self.backbone:
            self.config_dict = {
                'Global': Global_dict,
                'Arch': Arch_dict,
                'Loss': None,
                'DataLoader': {'Train': Train_dict, 'Eval': Val_dict, 'Test': Val_dict},
                'profiler_options': profiler_options
            }
        elif 'YOLOv5' in self.backbone:
            self.config_dict = {
                'Global': Global_dict,
                'Arch': Arch_dict,
                'Loss': YOLOv5Loss_dict,
                'DataLoader': {'Train': Train_dict, 'Eval': Val_dict, 'Test': Val_dict},
                'Augment': Yolo_Augment,
                'profiler_options': profiler_options
            }
        else:
            print('Error: not support eval mode!!!')
            sys.exit()

    def get_config(self) -> dict:
        # config_attrdict = AttrDict(self.config_dict)
        # create_attr_dict(config_attrdict)
        return self.config_dict


def overwrite(config):
    config_overwrite = Config(**config).get_config()
    print('Overwrite Successfully!!!')
    return config_overwrite


def is_hide(src_config: dict) -> bool:
    if isinstance(src_config, dict):
        if {'Global', 'AMP', 'Arch', 'Loss', 'Optimizer', 'DataLoader', 'Metric'}.issubset(set(src_config.keys())):
            print('Using Standard Config!!! Do not need to overwrite!!!')
            return False
        else:
            print('Using hide Config!!! Need to overwrite!!!')
            return True


