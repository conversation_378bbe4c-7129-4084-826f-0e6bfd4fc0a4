import os
import copy
import argparse
import yaml
import json
from pytorch.configs import config_taiho


class AttrDict(dict):
    def __getattr__(self, key):
        return self[key]

    def __setattr__(self, key, value):
        if key in self.__dict__:
            self.__dict__[key] = value
        else:
            self[key] = value

    def __deepcopy__(self, content):
        return copy.deepcopy(dict(self))

def parse_config(cfg_file):
    """Load a config file into AttrDict"""
    with open(cfg_file, 'r') as fopen:
        yaml_config = AttrDict(yaml.load(fopen, Loader=yaml.SafeLoader))
    create_attr_dict(yaml_config)
    return yaml_config

def create_attr_dict(yaml_config):
    from ast import literal_eval
    for key, value in yaml_config.items():
        if type(value) is dict:
            yaml_config[key] = value = AttrDict(value)
        if isinstance(value, str):
            try:
                value = literal_eval(value)
            except BaseException:
                pass
        if isinstance(value, AttrDict):
            create_attr_dict(yaml_config[key])
        else:
            yaml_config[key] = value

def override(dl, ks, v):
    def str2num(v):
        try:
            return eval(v)
        except Exception:
            return v
    assert isinstance(dl, (list, dict)), ("{} should be a list or a dict")
    assert len(ks) > 0, ('lenght of keys should larger than 0')
    if isinstance(dl, list):
        k = str2num(ks[0])
        if len(ks) == 1:
            assert k < len(dl), ('index({}) out of range({})'.format(k, dl))
            dl[k] = str2num(v)
        else:
            override(dl[k], ks[1:], v)
    else:
        if len(ks) == 1:
            if not ks[0] in dl:
                print('A new field ({}) detected!'.format(ks[0], dl))
            dl[ks[0]] = str2num(v)
        else:
            if ks[0] not in dl.keys():
                dl[ks[0]] = {}
                print("A new Series field ({}) detected!".format(ks[0], dl))
            override(dl[ks[0]], ks[1:], v)


def override_config(config, options=None):
    if options is not None:
        for opt in options:
            assert isinstance(opt, str), (
                "option({}) should be a str".format(opt))
            assert "=" in opt, (
                "option({}) should contain a ="
                "to distinguish between key and value".format(opt))
            pair = opt.split('=')
            assert len(pair) == 2, ("there can be only a = in the option")
            key, value = pair
            keys = key.split('.')
            override(config, keys, value)
    return config


def get_config(fname, overrides=None):
    """
    Read config from file
    """
    assert os.path.exists(fname), ('config file({}) is not exist'.format(fname))
    config = parse_config(fname)
    override_config(config, overrides)
    return config

def update_config(engine, project_root, material_name, folder_name, image_size, platform,
                  retain_size_info, update_mode, detection_model_id, model_dir):
    config = get_config(os.path.join(project_root, 'configs/config.yaml'), overrides=[])
    config.profiler_options = None
    dataset_root_dir = os.path.join(project_root, 'dataset')
    dataset_path = os.path.join(dataset_root_dir, material_name)
    contents = os.listdir(dataset_path)
    subfolders_list = [f for f in contents if os.path.isdir(os.path.join(dataset_path, f)) and os.listdir(
        os.path.join(dataset_path, f))]  # 过滤出子文件夹
    class_num = len(subfolders_list)
    # 以下代码为适应隐藏版的配置文件 BEGIN
    if config_taiho.is_hide(config):
        config['image_size'] = image_size
        config['retain_size_info'] = retain_size_info
        config['class_num'] = class_num
        config['platform'] = platform
        config['update_mode'] = update_mode
        config['detection_model_id'] = detection_model_id
        config = config_taiho.overwrite(config)
        config = AttrDict(config) 
        create_attr_dict(config)
    else:
        print(
            'Warning: config.yaml 不是简化的配置文件，eval_mode,image_size,retain_size_info,class_num 需要在config.yaml中手动输入!!!')
    # 以上代码为适应隐藏版的配置文件 END

    # 模型训练完成后的保存路径
    config['Global']['output_dir'] = os.path.join(project_root, 'models', material_name, folder_name)
    config['Global']['project_root'] = project_root

    # DataSets路径
    config['DataLoader']['Train']['dataset']['image_root'] = dataset_path
    config['DataLoader']['Train']['dataset']['cls_label_path'] = dataset_path + '/train_data_file.txt'
    config['DataLoader']['Eval']['dataset']['image_root'] = dataset_path
    config['DataLoader']['Eval']['dataset']['cls_label_path'] = dataset_path + '/eval_data_file.txt'
    config['DataLoader']['Train']['dataset']['background'] = os.path.join(dataset_root_dir, 'background')

    # codes writed by DYF at 20240416 BEGINE 增加数据增强 PasteInstance并设置参数
    for ops in config['DataLoader']['Train']['dataset']['transform_ops']:
        if ops.get('PasteInstance', False):
            ops['PasteInstance']['image_root'] = dataset_path
            ops['PasteInstance']['cls_label_path'] = dataset_path + '/train_data_file.txt'
    # codes writed by DYF at 20240416 END

    # 直接加载ImageNet预训练权重
    config['Global']['pretrained_model'] = os.path.join(project_root, 'models', 'init_model')
    config['Global']['material_name'] = material_name

    with open(f"{config['DataLoader']['Train']['dataset']['image_root']}/subfolder_to_id.json", 'r') as file:
        names = json.load(file)
    config['Global']['cls_name'] = names
    config['Global']['val_path'] = f"{dataset_root_dir}/{material_name}_valsets"
    config['Global']['model_dir'] = model_dir

    # print_dict(config, engine.LOGGER)
    engine.config = config

def print_dict(config, LOGGER, delimiter=0):
    placeholder = "-" * 60
    for k, v in sorted(config.items()):
        if isinstance(v, dict):
            LOGGER.info("{}{} : ".format(delimiter * " ", k))
            print_dict(v, LOGGER, delimiter + 4)
        elif isinstance(v, list) and len(v) >= 1 and isinstance(v[0], dict):
            LOGGER.info("{}{} : ".format(delimiter * " ", k))
            for value in v:
                print_dict(value, LOGGER, delimiter + 4)
        else:
            LOGGER.info("{}{} : {}".format(delimiter * " ", k, v))
        if k.isupper():
            LOGGER.info(placeholder)