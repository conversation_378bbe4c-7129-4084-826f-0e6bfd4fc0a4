
from functools import partial
import io
import six
import math
import random
import cv2
import numpy as np
from PIL import Image, ImageOps, __version__ as PILLOW_VERSION
from torchvision.transforms import functional as F
from torchvision import transforms
from .augmix import autocontrast, equalize, posterize, rotate, solarize, shear_x, shear_y, translate_x, translate_y, gamma_correction

class UnifiedResize(object):
    def __init__(self, interpolation=None, backend="cv2", return_numpy=True):
        _cv2_interp_from_str = {
            'nearest': cv2.INTER_NEAREST,
            'bilinear': cv2.INTER_LINEAR,
            'area': cv2.INTER_AREA,
            'bicubic': cv2.INTER_CUBIC,
            'lanczos': cv2.INTER_LANCZOS4,
            'random': (cv2.INTER_LINEAR, cv2.INTER_CUBIC)
        }
        _pil_interp_from_str = {
            'nearest': Image.NEAREST,
            'bilinear': Image.BILINEAR,
            'bicubic': Image.BICUBIC,
            'box': Image.BOX,
            'lanczos': Image.LANCZOS,
            'hamming': Image.HAMMING,
            'random': (Image.BILINEAR, Image.BICUBIC)
        }
        def _cv2_resize(src, size, resample):
            if isinstance(resample, tuple):
                resample = random.choice(resample)
            return cv2.resize(src, size, interpolation=resample)
        def _pil_resize(src, size, resample, return_numpy=True):
            if isinstance(resample, tuple):
                resample = random.choice(resample)
            if isinstance(src, np.ndarray):
                pil_img = Image.fromarray(src)
            else:
                pil_img = src
            pil_img = pil_img.resize(size, resample)
            if return_numpy:
                return np.asarray(pil_img)
            return pil_img
        if backend.lower() == "cv2":
            if isinstance(interpolation, str):
                interpolation = _cv2_interp_from_str[interpolation.lower()]
            elif interpolation is None:
                interpolation = cv2.INTER_LINEAR
            self.resize_func = partial(_cv2_resize, resample=interpolation)
        elif backend.lower() == "pil":
            if isinstance(interpolation, str):
                interpolation = _pil_interp_from_str[interpolation.lower()]
            self.resize_func = partial(_pil_resize, resample=interpolation, return_numpy=return_numpy)
        else:
            print(f"The backend of Resize only support \"cv2\" or \"PIL\". \"f{backend}\" is unavailable. Use \"cv2\" instead.")
            self.resize_func = cv2.resize
    def __call__(self, src, size):
        if isinstance(size, list):
            size = tuple(size)
        return self.resize_func(src, size)


class OperatorParamError(ValueError):
    pass


class DecodeImage(object):
    def __init__(self,
                 to_np=True,
                 to_rgb=True,
                 channel_first=False,
                 backend="pil"):
        self.to_np = to_np
        self.to_rgb = to_rgb
        self.channel_first = channel_first
        self.backend = backend
    def __call__(self, img):
        if isinstance(img, Image.Image):
            assert self.backend == "pil", "invalid input 'img' in DecodeImage"
        elif isinstance(img, np.ndarray):
            assert self.backend == "cv2", "invalid input 'img' in DecodeImage"
        else:
            raise ValueError("invalid input 'img' in DecodeImage")
        if self.to_np:
            if self.backend == "pil":
                assert img.mode == "RGB", f"invalid shape of image[{img.shape}]"
                img = np.array(img)[:, :, ::-1]  # BRG
            if self.to_rgb:
                assert img.shape[2] == 3, f"invalid shape of image[{img.shape}]"
                img = img[:, :, ::-1]
            if self.channel_first:
                img = img.transpose((2, 0, 1))
        return img


class ResizeImage(object):
    def __init__(self,
                 size=None,
                 resize_short=None,
                 interpolation=None,
                 backend="cv2",
                 return_numpy=True):
        if resize_short is not None and resize_short > 0:
            self.resize_short = resize_short
            self.w = None
            self.h = None
        elif size is not None:
            self.resize_short = None
            self.w = size if type(size) is int else size[0]
            self.h = size if type(size) is int else size[1]
        else:
            raise OperatorParamError("invalid params for ReisizeImage for ''both 'size' and 'resize_short' are None")
        self._resize_func = UnifiedResize(
            interpolation=interpolation,
            backend=backend,
            return_numpy=return_numpy)

    def __call__(self, img):
        if isinstance(img, np.ndarray):
            img_h, img_w = img.shape[:2]
        else:
            img_w, img_h = img.size
        if self.resize_short is not None:
            percent = float(self.resize_short) / min(img_w, img_h)
            w = int(round(img_w * percent))
            h = int(round(img_h * percent))
        else:
            w = self.w
            h = self.h
        return self._resize_func(img, (w, h))


class ResizeProportionImage(object):
    def __init__(self,
                 size=None,
                 interpolation=None,
                 backend="cv2",
                 return_numpy=True,
                 when_greater_do=False):
        if size is not None:
            self.w = size if type(size) is int else size[0]
            self.h = size if type(size) is int else size[1]
        else:
            raise OperatorParamError("invalid params for LetterBoxImage for '' 'size' is None")
        self.when_greater_do = when_greater_do
        self._resize_func = UnifiedResize(
            interpolation=interpolation,
            backend=backend,
            return_numpy=return_numpy)

    def __call__(self, img):
        if isinstance(img, np.ndarray):
            img_h, img_w = img.shape[:2]
        else:
            img_w, img_h = img.size
        if self.when_greater_do:
            if img_h < self.h and img_w < self.w:
                return img if isinstance(img, np.ndarray) else np.array(img)
        percent = min(float(self.w / img_w), float(self.h / img_h))
        w = int(round(img_w * percent))
        h = int(round(img_h * percent))
        return self._resize_func(img, (w, h))


class Padv3(object):
    def __init__(self,
                 size=None,
                 size_divisor=32,
                 pad_mode=0,
                 offsets=None,
                 fill_value=(127, 127, 127)):
        if not isinstance(size, (int, list)):
            raise TypeError(
                "Type of target_size is invalid when random_size is True. Must be List, now is {}".format(type(size)))
        if isinstance(size, int):
            size = [size, size]
        assert pad_mode in [-1, 0, 1, 2], 'currently only supports four modes [-1, 0, 1, 2]'
        if pad_mode == -1:
            assert offsets, 'if pad_mode is -1, offsets should not be None'
        self.size = size
        self.size_divisor = size_divisor
        self.pad_mode = pad_mode
        self.fill_value = fill_value
        self.offsets = offsets

    def apply_image(self, image, offsets, im_size, size):
        x, y = offsets
        im_h, im_w = im_size
        h, w = size
        canvas = np.ones((h, w, 3), dtype=image.dtype)
        canvas *= np.array(self.fill_value, dtype=image.dtype)
        canvas[y:y + im_h, x:x + im_w, :] = image
        return canvas

    def __call__(self, img):
        im_h, im_w = img.shape[:2]
        if self.size:
            w, h = self.size
            assert (im_h <= h and im_w <= w), '(h, w) of target size should be greater than (im_h, im_w)'
        else:
            h = int(np.ceil(im_h / self.size_divisor) * self.size_divisor)
            w = int(np.ceil(im_w / self.size_divisor) * self.size_divisor)
        if h == im_h and w == im_w:
            return img
        if self.pad_mode == -1:
            offset_x, offset_y = self.offsets
        elif self.pad_mode == 0:
            offset_y, offset_x = 0, 0
        elif self.pad_mode == 1:
            offset_y, offset_x = (h - im_h) // 2, (w - im_w) // 2
        else:
            offset_y, offset_x = h - im_h, w - im_w
        offsets, im_size, size = [offset_x, offset_y], [im_h, im_w], [h, w]
        return self.apply_image(img, offsets, im_size, size)



class RandFlipImage(object):
    def __init__(self, flip_code=1):
        assert flip_code in [-1, 0, 1], "flip_code should be a value in [-1, 0, 1]"
        self.flip_code = flip_code
    def __call__(self, img):
        if random.randint(0, 1) == 1:
            if isinstance(img, np.ndarray):
                return cv2.flip(img, self.flip_code)
            else:
                if self.flip_code == 1:
                    return img.transpose(Image.FLIP_LEFT_RIGHT)
                elif self.flip_code == 0:
                    return img.transpose(Image.FLIP_TOP_BOTTOM)
                else:
                    return img.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.FLIP_LEFT_RIGHT)
        else:
            return img


class NormalizeImage(object):
    def __init__(self,
                 scale=None,
                 mean=None,
                 std=None,
                 order='chw',
                 output_fp16=False,
                 channel_num=3):
        if isinstance(scale, str):
            scale = eval(scale)
        assert channel_num in [3, 4], "channel number of input image should be set to 3 or 4."
        self.channel_num = channel_num
        self.output_dtype = 'float16' if output_fp16 else 'float32'
        self.scale = np.float32(scale if scale is not None else 1.0 / 255.0)
        self.order = order
        mean = mean if mean is not None else [0.485, 0.456, 0.406]
        std = std if std is not None else [0.229, 0.224, 0.225]
        shape = (3, 1, 1) if self.order == 'chw' else (1, 1, 3)
        self.mean = np.array(mean).reshape(shape).astype('float32')
        self.std = np.array(std).reshape(shape).astype('float32')
    def __call__(self, img):
        from PIL import Image
        if isinstance(img, Image.Image):
            img = np.array(img)
        assert isinstance(img,np.ndarray), "invalid input 'img' in NormalizeImage"
        img = (img.astype('float32') * self.scale - self.mean) / self.std
        if self.channel_num == 4:
            img_h = img.shape[1] if self.order == 'chw' else img.shape[0]
            img_w = img.shape[2] if self.order == 'chw' else img.shape[1]
            pad_zeros = np.zeros(
                (1, img_h, img_w)) if self.order == 'chw' else np.zeros(
                    (img_h, img_w, 1))
            img = (np.concatenate(
                (img, pad_zeros), axis=0)
                   if self.order == 'chw' else np.concatenate(
                       (img, pad_zeros), axis=2))
        return img.astype(self.output_dtype)


class ToCHWImage(object):
    def __init__(self):
        pass
    def __call__(self, img):
        if isinstance(img, Image.Image):
            img = np.array(img)
        return img.transpose((2, 0, 1))


class AugMix(object):
    def __init__(self,
                 prob=0.5,
                 aug_prob_coeff=0.1,
                 mixture_width=3,
                 mixture_depth=1,
                 aug_severity=1,
                 proportion=0.20):
        self.prob = prob
        self.aug_prob_coeff = aug_prob_coeff
        self.mixture_width = mixture_width
        self.mixture_depth = mixture_depth
        self.aug_severity = aug_severity
        self.augmentations = [autocontrast, equalize, posterize, rotate, solarize,
             shear_x, shear_y, translate_x, translate_y, gamma_correction]
        self.proportion = proportion
    def __call__(self, image):
        if random.random() > self.prob:
            return np.asarray(image)
        ws = np.float32(
            np.random.dirichlet([self.aug_prob_coeff] * self.mixture_width))
        m = np.random.uniform(0, self.proportion)
        mix = np.zeros(image.shape)
        for i in range(self.mixture_width):
            image_aug = image.copy()
            image_aug = Image.fromarray(image_aug)
            depth = self.mixture_depth if self.mixture_depth > 0 else np.random.randint(
                1, 4)
            for _ in range(depth):
                op = np.random.choice(self.augmentations)
                image_aug = op(image_aug, self.aug_severity)
            mix += ws[i] * np.asarray(image_aug)
        mixed = (1 - m) * image + m * mix
        return mixed.astype(np.uint8)


class ColorJitter(object):
    def __init__(self, prob=0.2, brightness=0.05, contrast=0.01, saturation=0.01):
        self.prob = prob
        self.transform = transforms.ColorJitter(
            brightness=(1 - brightness, 1 + brightness) if brightness > 0 else None,
            contrast=(1 - contrast, 1 + contrast) if contrast > 0 else None,
            saturation=(1 - saturation, 1 + saturation) if saturation > 0 else None)
    def __call__(self, img):
        if np.random.random() >= self.prob:
            return img
        # 处理不同输入类型
        if isinstance(img, np.ndarray):
            img = Image.fromarray(np.ascontiguousarray(img))
            img = self.transform(img)
            return np.asarray(img)
        elif isinstance(img, Image.Image):
            return self.transform(img)
        else:
            raise TypeError(f"Unsupported image type: {type(img)}")


class RandomRotation(object):
    def __init__(self, prob=0.5, degrees=90, interpolation=F.InterpolationMode.BILINEAR, fill=[27,53,248], expand=True):
        self.prob = prob
        self.degrees = (-degrees, degrees)
        self.interpolation = interpolation
        self.expand = expand
        self.fill = fill
    def __call__(self, img):
        if np.random.random() < self.prob:
            img = Image.fromarray(img)
            img = self._apply_image(img)
            img = np.asarray(img)
        return img
    def _apply_image(self, img):
        angle = self._get_param(self.degrees)
        return F.rotate(img, angle, interpolation=self.interpolation, expand=self.expand, fill=self.fill)
    def _get_param(self, degrees):
        angle = random.uniform(degrees[0], degrees[1])
        return angle

class Pad(object):
    def __init__(self,
                 padding: int,
                 fill: int=0,
                 padding_mode: str="constant",
                 backend: str="pil"):
        self.padding = padding
        self.fill = fill
        self.padding_mode = padding_mode
        self.backend = backend
        assert backend in [
            "pil", "cv2"
        ], f"backend must in ['pil', 'cv2'], but got {backend}"

    def _parse_fill(self, fill, img, min_pil_version, name="fillcolor"):
        # Process fill color for affine transforms
        major_found, minor_found = (int(v)
                                    for v in PILLOW_VERSION.split('.')[:2])
        major_required, minor_required = (int(v) for v in
                                          min_pil_version.split('.')[:2])
        if major_found < major_required or (major_found == major_required and
                                            minor_found < minor_required):
            if fill is None:
                return {}
            else:
                msg = (
                    "The option to fill background area of the transformed image, "
                    "requires pillow>={}")
                raise RuntimeError(msg.format(min_pil_version))

        num_bands = len(img.getbands())
        if fill is None:
            fill = 0
        if isinstance(fill, (int, float)) and num_bands > 1:
            fill = tuple([fill] * num_bands)
        if isinstance(fill, (list, tuple)):
            if len(fill) != num_bands:
                msg = (
                    "The number of elements in 'fill' does not match the number of "
                    "bands of the image ({} != {})")
                raise ValueError(msg.format(len(fill), num_bands))

            fill = tuple(fill)

        return {name: fill}

    def __call__(self, img):
        if self.backend == "pil":
            opts = self._parse_fill(self.fill, img, "2.3.0", name="fill")
            if img.mode == "P":
                palette = img.getpalette()
                img = ImageOps.expand(img, border=self.padding, **opts)
                img.putpalette(palette)
                return img
            return ImageOps.expand(img, border=self.padding, **opts)
        else:
            img = cv2.copyMakeBorder(
                img,
                self.padding,
                self.padding,
                self.padding,
                self.padding,
                cv2.BORDER_CONSTANT,
                value=(self.fill, self.fill, self.fill))
            return img
