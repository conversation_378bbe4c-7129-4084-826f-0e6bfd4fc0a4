from .operators import OperatorParamError
import cv2
import numpy as np
import random


class BackgroundAug(object):
    """ Scale Image proportionally """

    def __init__(self,
                 prob=0,
                 bg_lower_hsv=None,
                 bg_upper_hsv=None
                 ):
        if type(bg_lower_hsv) is list and type(bg_upper_hsv) is list:
            self.bg_lower_hsv = np.array(bg_lower_hsv)
            self.bg_upper_hsv = np.array(bg_upper_hsv)
        else:
            raise OperatorParamError("invalid params for BackgroundAug for '\
                ' 'bg_lower_hsv' and 'bg_upper_hsv' is None")
        self.prob = prob

    def __call__(self, img):
        if np.random.random() < self.prob:
            img_random_bg = np.random.randint(0, 256, size=img.shape, dtype=img.dtype)

            hsv_instance = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
            # 定义要提取的蓝色范围 适用于1200机器下pinenut、almondshell，蓝色背景偏亮
            # lower_blue = np.array([100, 100, 100])
            # upper_blue = np.array([140, 255, 255])
            # 定义要提取的蓝色范围 适用于虾仁，蓝色背景偏暗
            # lower_blue = np.array([100, 70, 30])
            # upper_blue = np.array([140, 255, 255])

            # 全幅图都当成前景，适用于bottle, 前景颜色太多
            # lower_blue = np.array([0, 0, 0])
            # upper_blue = np.array([1, 1, 1])

            mask_bg = cv2.inRange(hsv_instance, self.bg_lower_hsv, self.bg_upper_hsv)
            mask_fg = cv2.bitwise_not(mask_bg)

            # 图像分割，提取instance前景区域,以及img_src的背景区域，然后合并
            instance_fg = cv2.bitwise_and(img, img, mask=mask_fg)
            img_bg = cv2.bitwise_and(img_random_bg, img_random_bg, mask=mask_bg)
            img = cv2.bitwise_or(instance_fg, img_bg)

        return img


class ObjectBGAug(object):
    def __init__(self, prob=0.5, bg_RGB = [15,15,15], bg_HSV=[0.15,0.2,0.2],
                 lowerHSV_thresh_bg=[100, 100, 100],upperHSV_thresh_bg = [140, 255, 255]):
        super().__init__()
        self.prob = prob
        self.bg_RGB = bg_RGB
        self.bg_HSV = bg_HSV
        self.lowerHSV_thresh_bg = lowerHSV_thresh_bg
        self.upperHSV_thresh_bg = upperHSV_thresh_bg

    def __call__(self, img):
        if np.random.random() < self.prob:
            self.lowerHSV_thresh_bg = np.array(self.lowerHSV_thresh_bg)
            self.upperHSV_thresh_bg = np.array(self.upperHSV_thresh_bg)
            img_hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
            mask = cv2.inRange(img_hsv, self.lowerHSV_thresh_bg, self.upperHSV_thresh_bg)
            background_mask = cv2.bitwise_not(mask)  # 获取背景的掩膜
            if random.random() < 0.5:
                B_thresh, G_thresh, R_thresh = self.bg_RGB
                random_nums = np.random.randint(-B_thresh, B_thresh, size=img.shape[:2] + (3,))  # 在RGB范围内生成随机数
                random_nums[..., 1] = np.random.randint(-G_thresh, G_thresh, size=img.shape[:2])
                random_nums[..., 2] = np.random.randint(-R_thresh, R_thresh, size=img.shape[:2])
                modified_pixels = np.clip(img + random_nums, 0, 255)
                img[np.where(background_mask == 0)] = modified_pixels[np.where(background_mask == 0)]  # 把modified_pixels和背景替换
            else:
                hsv_bg = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
                hsv_bg_parameter = self.bg_HSV
                hsv_bg_H, hsv_bg_S, hsv_bg_V = np.random.uniform(-1, 1, 3) * hsv_bg_parameter + 1
                hsv_bg[..., 0] = (hsv_bg[..., 0] * hsv_bg_H) % 180
                hsv_bg[..., 1] = np.clip(hsv_bg[..., 1] * hsv_bg_S, 0, 255)
                hsv_bg[..., 2] = np.clip(hsv_bg[..., 2] * hsv_bg_V, 0, 255)
                augmented_image = cv2.cvtColor(hsv_bg, cv2.COLOR_HSV2RGB)
                img[np.where(background_mask == 0)] = augmented_image[np.where(background_mask == 0)]
        return img