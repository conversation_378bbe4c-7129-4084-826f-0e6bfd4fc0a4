import math
import random
import os
import re
import cv2
import numpy as np

from pytorch.utils.general import (LOGGER, check_version, colorstr, resample_segments,
                                   segment2box, xywhn2xyxy, bbox_ioa)


def paste_instance(img_src, labels_src, img_instance, label_instance, org_position="M", hyp=None):
    img_src_w = img_src.shape[1]
    img_src_h = img_src.shape[0]
    img_instance_w = img_instance.shape[1]
    img_instance_h = img_instance.shape[0]

    # 如果实例图像的高度或宽度大于源图像，则需要缩小实例图像的尺寸
    if img_instance_h > img_src_h or img_instance_w > img_src_w:
        resize_ratio = max(img_instance.shape[0]/img_src.shape[0], img_instance.shape[1]/img_src.shape[1])
        width = int(img_instance.shape[1] / resize_ratio)
        height = int(img_instance.shape[0] / resize_ratio)
        dsize = (width, height)
        img_instance = cv2.resize(img_instance,  dsize=dsize)
        img_instance_w = img_instance.shape[1]
        img_instance_h = img_instance.shape[0]


    if org_position != "M":
        # X0orig, Y0orig, X1orig, Y1orig = org_position
        # 寻找合适的粘贴区域
        if org_position == "T" or org_position == "B":
            box = np.zeros(4)
            for i in range(10):
                cx_min = min(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
                cx_max = max(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
                cx = random.randint(cx_min, cx_max)
                xmin = cx - int(img_instance_w / 2)
                xmax = xmin + img_instance_w
                if img_instance_w == img_src_w:
                    xmin = 0
                    xmax = img_src_w
                if org_position == "T":
                    box_cur = np.array([xmin, 0, xmax, img_instance_h])
                else:
                    box_cur = np.array([xmin, int(img_src_h-img_instance_h), xmax, img_src_h])
                ioa = bbox_ioa(box_cur, labels_src[:, 1:5])  # 计算与源标签的交并比
                if (ioa < 0.10).all():  # 允许最多10%的重叠
                    box = box_cur
                    break
                else:
                    continue
        elif org_position == "L" or org_position == "R":
            box = np.zeros(4)
            for i in range(10):
                cy_min = min(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
                cy_max = max(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
                cy = random.randint(cy_min, cy_max)
                ymin = cy - int(img_instance_h / 2)
                ymax = ymin + img_instance_h
                if img_instance_h == img_src_h:
                    ymin = 0
                    ymax = img_src_h
                if org_position == "L":
                    box_cur = np.array([0, ymin, img_instance_w, ymax])
                else:
                    box_cur = np.array([img_src_w - img_instance_w, ymin, img_src_w, ymax])
                ioa = bbox_ioa(box_cur, labels_src[:, 1:5])  # 计算与源标签的交并比
                if (ioa < hyp['Augment']['ioa']).all():  # 允许最多hyp['Augment']['ioa']的重叠
                    box = box_cur
                    break
                else:
                    continue
    else:
        box = np.zeros(4) #xmin, ymin, xmax, ymax
        for i in range(10):
            # 随机选取粘贴的中心点，并计算粘贴区域矩形框坐标
            cx_min = min(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
            cx_max = max(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
            cy_min = min(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
            cy_max = max(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
            cx = random.randint(cx_min, cx_max)
            cy = random.randint(cy_min, cy_max)
            xmin = cx - int(img_instance_w / 2)
            ymin = cy - int(img_instance_h / 2)
            xmax = xmin + img_instance_w
            ymax = ymin + img_instance_h

            if img_instance_w == img_src_w:
                xmin = 0
                xmax = img_src_w

            if img_instance_h == img_src_h:
                ymin = 0
                ymax = img_src_h

            box_cur = np.array([xmin, ymin, xmax, ymax])

            ioa = bbox_ioa(box_cur, labels_src[:, 1:5])   # 计算与源标签的交并比
            if (ioa < hyp['Augment']['ioa']).all():  # 允许最多hyp['Augment']['ioa']的重叠
                box = box_cur
                break
            else:
                continue

    if np.all(box==0): #10次都没寻找到paste区域则直接返回原图
        return img_src, labels_src
    else:
        hsv_instance = cv2.cvtColor(img_instance, cv2.COLOR_BGR2HSV)
        # 定义要提取的蓝色范围 适用于1200机器下pinenut、almondshell，蓝色背景偏亮
        lower_blue = np.array(hyp['Augment']['instance_lower_blue'])
        upper_blue = np.array(hyp['Augment']['instance_upper_blue'])
        # print("lower_blue:",lower_blue)
        # print("upper_blue:",upper_blue)

        mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
        mask_fg = cv2.bitwise_not(mask_bg)

        # 图像分割，提取instance前景区域,以及img_src的背景区域，然后合并
        instance_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
        img_src_roi_bg = cv2.bitwise_and(img_src[box[1]:box[3], box[0]:box[2], :], img_src[box[1]:box[3], box[0]:box[2], :], mask=mask_bg)
        img_src[box[1]:box[3], box[0]:box[2], :] = cv2.bitwise_or(instance_fg, img_src_roi_bg)
        # 拼接label
        labels_src = np.concatenate((labels_src, [[label_instance, *box]]), 0)
        return img_src, labels_src


def sample_and_paste_instance(img_src, labels_src, instance_gallery, class_ratio, sample_scale, hyp):
    class_ratio =[int(round(it*class_ratio)) for it in sample_scale] # 实际粘贴图片的数量
    # 给单张图粘贴不同类别的图
    for cls_idx, sample_num in enumerate(class_ratio):
        instance_files = random.choices(instance_gallery[cls_idx][1], k=int(sample_num))
        # 粘贴单个类别的所有图片
        for instance_file in instance_files:
            img_instance = cv2.imread(instance_file)
            org_position = os.path.basename(instance_file).split(".")[0].split("_")[-1]
            org_position = org_position if org_position in ["L", "R", "T", "B"] else "M"
            img_src, labels_src = paste_instance(img_src, labels_src, img_instance, cls_idx, org_position, hyp)
    return img_src, labels_src






