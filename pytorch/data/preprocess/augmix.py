import cv2
import random
import numpy as np
from PIL import Image, ImageOps, ImageEnhance


def int_parameter(level, maxval):
    return int(level * maxval / 10)


def float_parameter(level, maxval):
    return float(level) * maxval / 10.


def sample_level(n):
    return np.random.uniform(low=0.1, high=n)


def autocontrast(pil_img, *args):
    return ImageOps.autocontrast(pil_img)


def equalize(pil_img, *args):
    return ImageOps.equalize(pil_img)


def posterize(pil_img, level, *args):
    level = int_parameter(sample_level(level), 4)
    return ImageOps.posterize(pil_img, 4 - level)


def rotate(pil_img, level, *args):
    degrees = int_parameter(sample_level(level), 30)
    if np.random.uniform() > 0.5:
        degrees = -degrees
    return pil_img.rotate(degrees, resample=Image.BILINEAR)


def solarize(pil_img, level, *args):
    level = int_parameter(sample_level(level), 256)
    return ImageOps.solarize(pil_img, 256 - level)


def shear_x(pil_img, level):
    level = float_parameter(sample_level(level), 0.3)
    if np.random.uniform() > 0.5:
        level = -level
    return pil_img.transform(pil_img.size,
                             Image.AFFINE, (1, level, 0, 0, 1, 0),
                             resample=Image.BILINEAR)


def shear_y(pil_img, level):
    level = float_parameter(sample_level(level), 0.3)
    if np.random.uniform() > 0.5:
        level = -level
    return pil_img.transform(pil_img.size,
                             Image.AFFINE, (1, 0, 0, level, 1, 0),
                             resample=Image.BILINEAR)


def translate_x(pil_img, level):
    level = int_parameter(sample_level(level), pil_img.size[0] / 3)
    if np.random.random() > 0.5:
        level = -level
    return pil_img.transform(pil_img.size,
                             Image.AFFINE, (1, 0, level, 0, 1, 0),
                             resample=Image.BILINEAR)


def translate_y(pil_img, level):
    level = int_parameter(sample_level(level), pil_img.size[1] / 3)
    if np.random.random() > 0.5:
        level = -level
    return pil_img.transform(pil_img.size,
                             Image.AFFINE, (1, 0, 0, 0, 1, level),
                             resample=Image.BILINEAR)


def color(pil_img, level, *args):
    level = float_parameter(sample_level(level), 1.8) + 0.1
    return ImageEnhance.Color(pil_img).enhance(level)


def contrast(pil_img, level, *args):
    level = float_parameter(sample_level(level), 1.8) + 0.1
    return ImageEnhance.Contrast(pil_img).enhance(level)


def brightness(pil_img, level, *args):
    level = float_parameter(sample_level(level), 1.8) + 0.1
    return ImageEnhance.Brightness(pil_img).enhance(level)


def sharpness(pil_img, level, *args):
    level = float_parameter(sample_level(level), 1.8) + 0.1
    return ImageEnhance.Sharpness(pil_img).enhance(level)


def gamma_correction(pil_img, *args):
    pil_img = np.array(pil_img)
    gamma = random.uniform(0.75, 1.25)
    inv_gamma = 1.0 / gamma  # 计算逆向Gamma变换
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)], dtype=np.uint8)
    pil_img = cv2.LUT(pil_img, table)
    return Image.fromarray(pil_img)


# class AugMix(object):
#     @classmethod
#     def fun(cls, image):
#         augmentations = [autocontrast, equalize, posterize, rotate, solarize,
#              shear_x, shear_y, translate_x, translate_y, gamma_correction]
#         proportion = 0.25
#         ws = np.float32(np.random.dirichlet([0.1] * 3))
#         m = np.random.uniform(0, proportion)
#         mix = np.zeros(image.shape)
#         for i in range(3):
#             image_aug = image.copy()
#             image_aug = Image.fromarray(image_aug)
#             op = np.random.choice(augmentations)
#             image_aug = op(image_aug, 1)
#             mix += ws[i] * np.asarray(image_aug)
#         mixed = (1 - m) * image + m * mix
#         return mixed.astype(np.uint8)


if __name__ == "__main__":
    import yaml
    image = cv2.imread(r"D:\work\datasets\2K_Seed\test\20240816183659011_0_000.png")
    hyp = r"C:\work\Yolo_Train\YOLOV5_v6.0_ORIGNAL_InsPaste_AUG\data\hyps\hyp.scratch-instanceAUG-Seed_change.yaml"
    if isinstance(hyp, str):
        with open(hyp, errors='ignore') as f:
            hyp = yaml.safe_load(f)  # load hyps dict
   
    
    # aug = AugMix(prob=1, aug_prob_coeff=hyp["aug_prob_coeff"], mixture_width=hyp["mixture_width"],
    #             mixture_depth=hyp["mixture_depth"], aug_severity=hyp["aug_severity"], proportion=hyp['AugMix_proportion'])
    # img0 = aug(image)
    #
    # cv2.imshow("image",image)
    # cv2.imshow("img0",img0)
    # cv2.waitKey()