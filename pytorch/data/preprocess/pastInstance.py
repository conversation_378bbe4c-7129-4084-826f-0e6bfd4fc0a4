import random
from .operators import OperatorParamError
import cv2
import numpy as np
import os


class PasteInstance(object):
    """ Scale Image proportionally """

    def __init__(self,
                 image_root,
                 cls_label_path,
                 prob=0,
                 sample_num=0,
                 bg_lower_hsv=None,
                 bg_upper_hsv=None,
                 fg_overlap_ratio_upperLimit=0,
                 instance_area_lowLimit=5,
                 delimiter=None,
                 ):
        if type(bg_lower_hsv) is list and type(bg_upper_hsv) is list:
            self.bg_lower_hsv = np.array(bg_lower_hsv)
            self.bg_upper_hsv = np.array(bg_upper_hsv)
        else:
            raise OperatorParamError("invalid params for BackgroundAug for '\
                ' 'bg_lower_hsv' and 'bg_upper_hsv' is None")
        self.image_root = image_root
        self.cls_label_path = cls_label_path
        self.prob = prob
        self.sample_num = sample_num
        self.fg_overlap_ratio_upperLimit = fg_overlap_ratio_upperLimit
        self.instance_area_lowLimit = instance_area_lowLimit
        self.delimiter = delimiter if delimiter is not None else " "

        self._load_anno()

    def _load_anno(self):
        assert os.path.exists(
            self.cls_label_path), f"path {self.cls_label_path} does not exist."
        assert os.path.exists(
            self.image_root), f"path {self.image_root} does not exist."
        self.images = []

        with open(self.cls_label_path) as fd:
            lines = fd.readlines()
            for line in lines:
                line = line.strip().split(self.delimiter)
                self.images.append(os.path.join(self.image_root, line[0]))
                assert os.path.exists(self.images[
                                          -1]), f"path {self.images[-1]} does not exist."

    def __call__(self, img):
        if np.random.random() < self.prob:
            src_img_hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
            src_img_mask_bg = cv2.inRange(src_img_hsv, self.bg_lower_hsv, self.bg_upper_hsv)
            src_img_mask_fg = cv2.bitwise_not(src_img_mask_bg)

            src_img_mask_bg_roi0_area = np.sum(src_img_mask_bg[0:int(img.shape[0] / 2), 0:int(img.shape[1] / 2)])
            src_img_mask_bg_roi1_area = np.sum(src_img_mask_bg[0:int(img.shape[0] / 2), int(img.shape[1] / 2):])
            src_img_mask_bg_roi2_area = np.sum(src_img_mask_bg[int(img.shape[0] / 2):, 0:int(img.shape[1] / 2)])
            src_img_mask_bg_roi3_area = np.sum(src_img_mask_bg[int(img.shape[0] / 2):, int(img.shape[1] / 2):])

            area_arr = np.array([src_img_mask_bg_roi0_area, src_img_mask_bg_roi1_area, src_img_mask_bg_roi2_area, src_img_mask_bg_roi3_area])
            point = np.argmax(area_arr) # 0:左上 1:右上 2:左下 3:右下, 寻找粘贴角点

            sample_imgs = random.sample(self.images, k=int(self.sample_num)) if int(self.sample_num) < len(
                self.images) else self.images

            paste_success = False

            for instance_img_file in sample_imgs:
                if paste_success:
                    break
                paste_count = 0
                instance_img = cv2.imread(instance_img_file)
                instance_img_rgb = cv2.cvtColor(instance_img, cv2.COLOR_BGR2RGB)
                instance_img_hsv = cv2.cvtColor(instance_img_rgb, cv2.COLOR_RGB2HSV)
                instance_mask_bg = cv2.inRange(instance_img_hsv, self.bg_lower_hsv, self.bg_upper_hsv)
                instance_mask_fg = cv2.bitwise_not(instance_mask_bg)

                while not paste_success and paste_count < 5:
                    paste_count += 1
                    wh = random.randint(0, min(img.shape[0], img.shape[1]))
                    if wh > min(instance_img.shape[0], instance_img.shape[1]):
                        wh = min(instance_img.shape[0], instance_img.shape[1])

                    if point == 0:
                        src_roi_xmin = 0
                        src_roi_ymin = 0
                        src_roi_xmax = wh
                        src_roi_ymax = wh
                        instance_roi_xmin = instance_img.shape[1] - wh
                        instance_roi_ymin = instance_img.shape[0] - wh
                        instance_roi_xmax = instance_img.shape[1]
                        instance_roi_ymax = instance_img.shape[0]
                    elif point == 1:
                        src_roi_xmin = img.shape[1] - wh
                        src_roi_ymin = 0
                        src_roi_xmax = img.shape[1]
                        src_roi_ymax = wh
                        instance_roi_xmin = 0
                        instance_roi_ymin = instance_img.shape[0] - wh
                        instance_roi_xmax = wh
                        instance_roi_ymax = instance_img.shape[0]
                    elif point == 2:
                        src_roi_xmin = 0
                        src_roi_ymin = img.shape[0] - wh
                        src_roi_xmax = wh
                        src_roi_ymax = img.shape[0]
                        instance_roi_xmin = instance_img.shape[1] - wh
                        instance_roi_ymin = 0
                        instance_roi_xmax = instance_img.shape[1]
                        instance_roi_ymax = wh
                    else:
                        src_roi_xmin = img.shape[1] - wh
                        src_roi_ymin = img.shape[0] - wh
                        src_roi_xmax = img.shape[1]
                        src_roi_ymax = img.shape[0]
                        instance_roi_xmin = 0
                        instance_roi_ymin = 0
                        instance_roi_xmax = wh
                        instance_roi_ymax = wh

                    instance_mask_fg_roi = instance_mask_fg[instance_roi_ymin:instance_roi_ymax, instance_roi_xmin:instance_roi_xmax]
                    if np.sum(instance_mask_fg_roi) < self.instance_area_lowLimit * 255:
                        continue

                    src_img_mask_fg_roi = src_img_mask_fg[src_roi_ymin:src_roi_ymax, src_roi_xmin:src_roi_xmax]
                    if np.sum(src_img_mask_fg_roi)/np.sum(src_img_mask_fg) > self.fg_overlap_ratio_upperLimit:
                        continue

                    # 图像分割，提取instance前景区域,以及img_src的背景区域，然后合并
                    instance_fg_roi = cv2.bitwise_and(instance_img_rgb[instance_roi_ymin:instance_roi_ymax, instance_roi_xmin:instance_roi_xmax, :],
                                                      instance_img_rgb[instance_roi_ymin:instance_roi_ymax, instance_roi_xmin:instance_roi_xmax, :],
                                                      mask=instance_mask_fg[instance_roi_ymin:instance_roi_ymax, instance_roi_xmin:instance_roi_xmax])
                    src_bg_roi = cv2.bitwise_and(
                        img[src_roi_ymin:src_roi_ymax, src_roi_xmin:src_roi_xmax, :],
                        img[src_roi_ymin:src_roi_ymax, src_roi_xmin:src_roi_xmax, :],
                        mask=instance_mask_bg[instance_roi_ymin:instance_roi_ymax, instance_roi_xmin:instance_roi_xmax])

                    img[src_roi_ymin:src_roi_ymax, src_roi_xmin:src_roi_xmax, :] = cv2.bitwise_or(instance_fg_roi, src_bg_roi)
                    paste_success = True
                    break

        return img
