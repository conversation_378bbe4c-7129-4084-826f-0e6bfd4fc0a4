# YOLOv5 🚀 by Ultralytics, GPL-3.0 license
"""
Dataloaders and dataset utils
"""

import glob
import hashlib
import json
import os
import random
import shutil
import time
import sys
from itertools import repeat
from multiprocessing.pool import Pool, ThreadPool
from pathlib import Path
from threading import Thread
from zipfile import ZipFile
from multiprocessing import Manager
import cv2
import numpy as np
import torch
import torch.nn.functional as F
import yaml
from PIL import ExifTags, Image, ImageOps
from torch.utils.data import DataLoader, Dataset, dataloader, distributed
from tqdm import tqdm
import platform

from pytorch.data.preprocess.augmentations import Albumentations, augment_hsv, copy_paste, letterbox, mixup, random_perspective
from pytorch.utils.general import (LOGGER, check_dataset, check_requirements, check_yaml, clean_str, xyn2xy,
                           xywh2xyxy, xywhn2xyxy, xyxy2xywhn, torch_distributed_zero_first)

from pytorch.data.preprocess.instance_paste import sample_and_paste_instance
from pytorch.data.dataloader.speed_dataloader import analyze_dataset_txt, optimized_epoch_batch , speed_sample_and_paste_instance
from multiprocessing import Manager

# Parameters
HELP_URL = 'https://github.com/ultralytics/yolov5/wiki/Train-Custom-Data'
IMG_FORMATS = ['bmp', 'jpg', 'jpeg', 'png', 'tif', 'tiff', 'dng', 'webp', 'mpo']  # acceptable image suffixes
VID_FORMATS = ['mov', 'avi', 'mp4', 'mpg', 'mpeg', 'm4v', 'wmv', 'mkv']  # acceptable video suffixes
WORLD_SIZE = int(os.getenv('WORLD_SIZE', 1))  # DPP
NUM_THREADS = min(8, max(1, os.cpu_count() - 1))  # number of multiprocessing threads

SAVE_NUM = 0

# Get orientation exif tag
for orientation in ExifTags.TAGS.keys():
    if ExifTags.TAGS[orientation] == 'Orientation':
        break

def get_hash(paths):
    # Returns a single hash value of a list of paths (files or dirs)
    size = sum(os.path.getsize(p) for p in paths if os.path.exists(p))  # sizes
    h = hashlib.md5(str(size).encode())  # hash sizes
    h.update(''.join(paths).encode())  # hash paths
    return h.hexdigest()  # return hash


def generate_and_save_val_data(background_folder, instance_path, hyp, val_size, class_names,
                               cls_id, imgsz, config):
    """
    Generate validation images and save them to disk with proper YOLOv5 labels.
    """
    # Create directories to save the data
    parent_dir = os.path.dirname(background_folder)
    val_dir = config['Global']['val_path']
    images_dir = os.path.join(val_dir, 'images')
    labels_dir = os.path.join(val_dir, 'labels')
    sample_ratio = hyp['DataLoader']['Eval']['dataset']['sample_ratio']
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(labels_dir, exist_ok=True)

    # Get background images
    background_files = [os.path.join(background_folder, f) for f in os.listdir(background_folder) if f.lower().endswith(('.jpg', '.png', '.bmp'))]
    assert len(background_files) > 0, f"No background images found in {background_folder}"

    # instance_gallery
    instance_gallery = {key: [n, []] for (key, n) in enumerate(class_names)}  # Instance image category dictionary
    with open(instance_path) as file:
        lines = file.readlines()
        for l in lines:
            l = l.strip().split()
            imgPath = l[0]
            clsName = l[1]
            cls_idx = cls_id[clsName]
            if instance_gallery[cls_idx][0] == clsName:
                instance_gallery[cls_idx][1].append(imgPath)
            else:
                raise Exception(f'the cls_idx in instance_gallery is not consistent with cls_n, please check it!!!')

    val_data = []  # List to save all generated image paths
    # Generate and save images
    for i in range(val_size):
        # Randomly select a background image
        background_image_path = random.choice(background_files)
        background_img = cv2.imread(background_image_path)
        background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))

        # Initial labels are empty
        labels = np.empty((0, 5), dtype=np.float32)

        # Use instance pasting for data augmentation
        sample_scale_cur = [random.random() for _ in range(len(class_names))]
        background_img, labels = sample_and_paste_instance(background_img, labels, instance_gallery, sample_ratio, sample_scale_cur, config)

        # Normalize labels (YOLOv5 format: class index, x_center, y_center, width, height)

        # if labels.size > 0:
        h, w = imgsz  # Height and width of the image
        norm_labels = []
        for label in labels:
            xmin, ymin, xmax, ymax = label[1], label[2], label[3], label[4]

            # Calculate center and width/height
            x_center = (xmin + xmax) / 2
            y_center = (ymin + ymax) / 2
            width = xmax - xmin
            height = ymax - ymin

            # Normalize to [0, 1]
            norm_x = x_center / w
            norm_y = y_center / h
            norm_w = width / w
            norm_h = height / h

            # Save the normalized label
            norm_labels.append([int(label[0]), norm_x, norm_y, norm_w, norm_h])

        # Save label
        label_filename = os.path.join(labels_dir, f"val_img_{i}.txt")
        np.savetxt(label_filename, norm_labels, fmt='%.6f')

        # Save the generated image
        img_filename = os.path.join(images_dir, f"val_img_{i}.bmp")

        # Save image
        cv2.imwrite(img_filename, background_img)

        # Add to val_data list, only save the image path
        val_data.append(img_filename)

    # Generate val.txt file to save image paths
    val_path = f"{config['Global']['val_path']}/val.txt"
    with open(val_path, 'w') as f:
        for img_path in val_data:
            f.write(f"{img_path}\n")


def exif_size(img):
    # Returns exif-corrected PIL size
    s = img.size  # (width, height)
    try:
        rotation = dict(img._getexif().items())[orientation]
        if rotation == 6:  # rotation 270
            s = (s[1], s[0])
        elif rotation == 8:  # rotation 90
            s = (s[1], s[0])
    except:
        pass

    return s


def exif_transpose(image):
    """
    Transpose a PIL image accordingly if it has an EXIF Orientation tag.
    Inplace version of https://github.com/python-pillow/Pillow/blob/master/src/PIL/ImageOps.py exif_transpose()

    :param image: The image to transpose.
    :return: An image.
    """
    exif = image.getexif()
    orientation = exif.get(0x0112, 1)  # default 1
    if orientation > 1:
        method = {2: Image.FLIP_LEFT_RIGHT,
                  3: Image.ROTATE_180,
                  4: Image.FLIP_TOP_BOTTOM,
                  5: Image.TRANSPOSE,
                  6: Image.ROTATE_270,
                  7: Image.TRANSVERSE,
                  8: Image.ROTATE_90,
                  }.get(orientation)
        if method is not None:
            image = image.transpose(method)
            del exif[0x0112]
            image.info["exif"] = exif.tobytes()
    return image


class InfiniteDataLoader(dataloader.DataLoader):
    """ Dataloader that reuses workers

    Uses same syntax as vanilla DataLoader
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        object.__setattr__(self, 'batch_sampler', _RepeatSampler(self.batch_sampler))
        self.iterator = super().__iter__()

    def __len__(self):
        return len(self.batch_sampler.sampler)

    def __iter__(self):
        for i in range(len(self)):
            yield next(self.iterator)


class _RepeatSampler:
    """ Sampler that repeats forever

    Args:
        sampler (Sampler)
    """

    def __init__(self, sampler):
        self.sampler = sampler

    def __iter__(self):
        while True:
            yield from iter(self.sampler)


def load_image_from_path(image_path):
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"Image not found: {image_path}")
    return img

# write by FanKun 20250214
class LoadImagesAndLabelsInstanceAUG(Dataset):
    # YOLOv5 train_loader/val_loader, loads images and labels for training and validation
    def __init__(self, instance_path=None, class_names=None, batch_size=16, imgsz=[192,1024], augment=True,
                 hyp=None,  single_cls=False, stride=32,
                 pad=0.0, prefix='', train_size=800, background_path=None):
        self.augment = augment
        self.imgsz = imgsz
        self.hyp = hyp
        self.stride = stride
        self.albumentations = Albumentations() if augment else None
        self.train_size = train_size  # Training set size
        self.class_names = class_names
        #####################################20250702修改 by LYH ########################################################
        mgr = Manager()
        self.epoch_batch = mgr.list()

        #Load background_img
        # self.background_img = mgr.list()
        self.background_img = []
        self.background_files = [os.path.join(background_path, f) for f in os.listdir(background_path) if f.lower().endswith(('.jpg', '.png', '.bmp'))]
        self.background_img_Num = hyp['Augment']['background_img_Num']
        for i in range(min(len(self.background_files), self.background_img_Num)):
            self.background_img.append(cv2.resize(cv2.imread(self.background_files[i]), dsize=(self.imgsz[1], self.imgsz[0])))
        assert len(self.background_files) > 0, f"No background images found in {background_path}"
        self.background_len = len(self.background_img)

        #Load instance_img_gallery /  instance_gallery
        # self.instance_img_gallery = mgr.dict()
        self.instance_img_gallery = {}
        instance_gallery = analyze_dataset_txt(instance_path)
        lower_blue = np.array(hyp['Augment']['bg_lower_hsv'])
        upper_blue = np.array(hyp['Augment']['bg_upper_hsv'])
        instance_img_gallery = {}
        self.instance_gallery = {}
        for clsName, imgLists in instance_gallery.items():
            self.instance_gallery[clsName] = []
            instance_img_gallery[clsName]={}
            for index, (path, size) in enumerate(imgLists):
                pos = os.path.basename(path).split(".")[0].split("_")[-1] if os.path.basename(path).split(".")[0].split("_")[-1] in ["L", "R", "T","B"] else "M"
                img = cv2.imread(path)
                hsv_instance = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
                mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
                instance_img_gallery[clsName][index]=[img, mask_bg, size] 
                self.instance_gallery[clsName].append([index, size, pos]) 
        self.instance_img_gallery = instance_img_gallery
        
        #################################################################################################################
        self.paste_instance_prob = 0.98 
        # Initialize empty label list
        dataset_size = self.train_size
        self.labels = [np.empty((0, 5), dtype=np.float32) for _ in range(dataset_size)]
    
    def __len__(self):
        return self.train_size
    
    def __getitem__(self, index):
        hyp = self.hyp
        img = random.choice(self.background_img).copy()
        batch_info = self.epoch_batch[index] if index<len(self.epoch_batch) else random.choice(self.epoch_batch)  
        
        ## img, labels = speed_sample_and_paste_instance(img, batch_info, self.instance_img_gallery, self.class_names)
        labels = []
        for cls, img_index, box, org_P in batch_info:
            img_instance, mask_bg, size = self.instance_img_gallery[cls][img_index]
            mask_fg = cv2.bitwise_not(mask_bg)
            x1, y1, x2, y2 = box[0], box[1], box[2], box[3]
            roi_src = img[y1:y2, x1:x2]
            roi_bg = cv2.bitwise_and(roi_src, roi_src, mask=mask_bg)
            roi_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
            img[y1:y2, x1:x2] = cv2.bitwise_or(roi_fg, roi_bg)
            labels.append([int(self.class_names[cls]), *[float(i) for i in box]])
        labels = np.array(labels)
        img = np.array(img)

        # labels = np.zeros((5,5))
        nl = len(labels)  # number of labels
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1E-3)

        # if hyp['Augment']['flip']:
        #     if random.random() < 0.3:
        #         img = np.flipud(img)
        #         if nl:
        #             labels[:, 2] = 1 - labels[:, 2]

        #     if random.random() < 0.3:
        #         img = np.fliplr(img)
        #         if nl:
        #             labels[:, 1] = 1 - labels[:, 1]

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        img = np.ascontiguousarray(img)
        # img = img.transpose((2, 0, 1))
        return torch.from_numpy(img), labels_out  #, background_image_path, shapes

    @staticmethod
    def collate_fn(batch):
        img, label = zip(*batch)  # transposed
        for i, l in enumerate(label):
            l[:, 0] = i  # add target image index for build_targets()
        return torch.stack(img, 0), torch.cat(label, 0)


def img2label_paths(img_paths):
    sa, sb = os.sep + 'images' + os.sep, os.sep + 'labels' + os.sep  # /images/, /labels/ substrings
    return [sb.join(x.rsplit(sa, 1)).rsplit('.', 1)[0] + '.txt' for x in img_paths]


class LoadImagesAndLabels(Dataset):
    cache_version = 0.6
    def __init__(self, path, batch_size=16, augment=False, hyp=None, rect=False, image_weights=False,
                 cache_images=False, single_cls=False, stride=32, pad=0.0, prefix=''):
        self.hyp = hyp
        self.stride = stride
        self.path = path
        f = []
        p = Path(path)
        with open(path) as t:
            t = t.read().strip().splitlines()
            parent = str(p.parent) + os.sep
            f += [x.replace('./', parent) if x.startswith('./') else x for x in t]  # local to global path
        self.img_files = sorted(x.replace('/', os.sep) for x in f if x.split('.')[-1].lower() in IMG_FORMATS)
        assert self.img_files, f'{prefix}No images found'

        self.label_files = img2label_paths(self.img_files)  # labels
        self.x = {}
        nm, nf, ne, nc, msgs,  = 0, 0, 0, 0, []
        pbar = tqdm(zip(self.img_files, self.label_files), total=len(self.img_files))
        for im_file, l in pbar:
            im_file, l, shape, nm_f, nf_f, ne_f, nc_f, msg = verify_image_label(im_file, l)
            nm += nm_f
            nf += nf_f
            ne += ne_f
            nc += nc_f
            if im_file:
                self.x[im_file] = [l, shape]
            if msg:
                msgs.append(msg)
        LOGGER.info(f"{nf} found, {nm} missing, {ne} empty, {nc} corrupted")
        pbar.close()
        if msgs:
            LOGGER.info('\n'.join(msgs))
        n = len(self.img_files)
        labels, shapes = zip(*self.x.values())
        self.labels = list(labels)
        self.img_files = list(self.x.keys())  # update
        self.label_files = img2label_paths(self.x.keys())  # update
        bi = np.floor(np.arange(n) / batch_size).astype(np.int64)  # batch index
        nb = bi[-1] + 1  # number of batches
        self.batch = bi  # batch index of image
        self.n = n
        self.indices = range(n)
        self.imgs, self.img_npy = [None] * n, [None] * n

    def __len__(self):
        return len(self.img_files)

    def __getitem__(self, index):
        index = self.indices[index]
        img, (h0, w0), (h, w) = load_image(self, index)
        # Letterbox
        # shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size  # final letterboxed shape
        # img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
        # shapes = (h0, w0), ((h / h0, w / w0), pad)  # for COCO mAP rescaling
        labels = self.labels[index].copy()
        nl = len(labels)  # update after albumentations
        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)
        img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        img = np.ascontiguousarray(img).astype(np.float32)
        return torch.from_numpy(img / 255), labels_out, self.img_files[index] #, shapes

    @staticmethod
    def collate_fn(batch):
        img, label, path  = zip(*batch)  # transposed
        for i, l in enumerate(label):
            l[:, 0] = i  # add target image index for build_targets()
        return torch.stack(img, 0), torch.cat(label, 0) # , path #, shapes



def create_dataloader(path, batch_size, stride, single_cls=False, hyp=None, augment=False, cache=False, pad=0.0,
                      rect=False, rank=-1, workers=8, image_weights=False, quad=False, prefix='', shuffle=False):
    with torch_distributed_zero_first(rank):
        dataset = LoadImagesAndLabels(path, batch_size,
                                  augment=augment,  # augmentation
                                  hyp=hyp,  # hyperparameters
                                  rect=rect,  # rectangular batches
                                  cache_images=cache,
                                  single_cls=single_cls,
                                  stride=int(stride),
                                  pad=pad,
                                  image_weights=image_weights,
                                  prefix=prefix)

    batch_size = min(batch_size, len(dataset))
    if sys.platform.startswith('linux'):
        nw = min([os.cpu_count() // WORLD_SIZE, batch_size if batch_size > 1 else 0, 8])  # number of workers
    else:
        nw = 0
    sampler = None if rank == -1 else distributed.DistributedSampler(dataset, shuffle=shuffle)
    loader = DataLoader if image_weights else InfiniteDataLoader  # only DataLoader allows for attribute updates
    return loader(dataset,
                  batch_size=batch_size,
                  shuffle=shuffle and sampler is None,
                  num_workers=nw,  # 部署时改为nw
                  sampler=sampler,
                  pin_memory=True,
                  collate_fn=LoadImagesAndLabels.collate_fn), dataset



# Ancillary functions --------------------------------------------------------------------------------------------------
def load_image(self, i):
    # loads 1 image from dataset index 'i', returns im, original hw, resized hw
    im = self.imgs[i]
    if im is None:  # not cached in ram
        npy = self.img_npy[i]
        if npy and npy.exists():  # load npy
            im = np.load(npy)
        else:  # read image
            path = self.img_files[i]
            im = cv2.imread(path)  # BGR
            assert im is not None, f'Image Not Found {path}'
        h0, w0 = im.shape[:2]  # orig hw
        # r = self.img_size[0] / max(h0, w0)  # ratio
        r = 1
        #if r != 1:  # if sizes are not equal
           # im = cv2.resize(im, (int(w0 * r), int(h0 * r)),
                            #interpolation=cv2.INTER_AREA if r < 1 and not self.augment else cv2.INTER_LINEAR)
        return im, (h0, w0), im.shape[:2]  # im, hw_original, hw_resized
    else:
        return self.imgs[i], self.img_hw0[i], self.img_hw[i]  # im, hw_original, hw_resized


def verify_image_label(im_file, lb_file):
    # Verify one image-label pair
    # im_file, lb_file, prefix = args
    nm, nf, ne, nc, msg, segments = 0, 0, 0, 0, '', []  # number (missing, found, empty, corrupt), message, segments
    try:
        # verify images
        im = Image.open(im_file)
        im.verify()  # PIL verify
        shape = exif_size(im)  # image size
        assert (shape[0] > 9) & (shape[1] > 9), f'image size {shape} <10 pixels'
        assert im.format.lower() in IMG_FORMATS, f'invalid image format {im.format}'
        if im.format.lower() in ('jpg', 'jpeg'):
            with open(im_file, 'rb') as f:
                f.seek(-2, 2)
                if f.read() != b'\xff\xd9':  # corrupt JPEG
                    ImageOps.exif_transpose(Image.open(im_file)).save(im_file, 'JPEG', subsampling=0, quality=100)
                    msg = f'{im_file}: corrupt JPEG restored and saved'
        # verify labels
        if os.path.isfile(lb_file):
            nf = 1  # label found
            with open(lb_file) as f:
                l = [x.split() for x in f.read().strip().splitlines() if len(x)]
                l = np.array(l, dtype=np.float32)
            nl = len(l)
            if nl:
                assert l.shape[1] == 5, f'labels require 5 columns, {l.shape[1]} columns detected'
                assert (l >= 0).all(), f'negative label values {l[l < 0]}'
                assert (l[:, 1:] <= 1).all(), f'non-normalized or out of bounds coordinates {l[:, 1:][l[:, 1:] > 1]}'
                _, i = np.unique(l, axis=0, return_index=True)
                if len(i) < nl:  # duplicate row check
                    l = l[i]  # remove duplicates
                    msg = f'{im_file}: {nl - len(i)} duplicate labels removed'
            else:
                ne = 1  # label empty
                l = np.zeros((0, 5), dtype=np.float32)
        else:
            nm = 1  # label missing
            l = np.zeros((0, 5), dtype=np.float32)
        return im_file, l, shape, nm, nf, ne, nc, msg
    except Exception as e:
        nc = 1
        msg = f'{im_file}: ignoring corrupt image/label: {e}'
        return nm, nf, ne, nc, msg

