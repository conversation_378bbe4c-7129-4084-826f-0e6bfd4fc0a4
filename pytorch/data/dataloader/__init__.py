from torchvision import datasets, transforms
from .CustomTransform import *
from .CustomImageDataset import *
from pytorch.data.dataloader.create_dataloader import create_dataloader, generate_and_save_val_data, LoadImagesAndLabelsInstanceAUG, InfiniteDataLoader
from pytorch.utils.general import colorstr
from torch.utils.data import DataLoader, WeightedRandomSampler
import sys
from pytorch.data.dataloader.speed_dataloader import analyze_dataset_txt
from pytorch.utils.general import torch_distributed_zero_first
from prefetch_generator import BackgroundGenerator

WORLD_SIZE = int(os.getenv('WORLD_SIZE', 1))


class DataLoaderX(DataLoader):
    def __iter__(self):
        return BackgroundGenerator(super().__iter__())

def build_mobilenetV3_dataLoader(engine):
    batch_size = engine.config['Global']['batch_size']
    engine.train_datasets = CustomImageDataset(engine.config['DataLoader']['Train']['dataset']['cls_label_path'],
                                       config=engine.config,
                                       mode='train')
    engine.val_datasets = CustomImageDataset(engine.config['DataLoader']['Eval']['dataset']['cls_label_path'],
                                     config=engine.config,
                                     mode='valid')
    if sys.platform.startswith('linux'):
        nw = min([os.cpu_count() // WORLD_SIZE, batch_size if batch_size > 1 else 0, 8])  # number of workers
    else:
        nw = 0

    train_loader = DataLoader(engine.train_datasets, batch_size=batch_size, shuffle=True, drop_last=True, num_workers=nw)
    engine.train_loader = ForeverDataIterator(train_loader)

    engine.val_loader = DataLoader(engine.val_datasets, batch_size=batch_size, shuffle=False, num_workers=nw)



def build_yolov5_dataLoader(engine):
    gs = max(int(engine.model.stride.max()), 32)
    batch_size = engine.config['Global']['batch_size']
    imgsz = engine.config['Global']['image_size']
    background_path = f"{engine.config['Global']['project_root']}/dataset/background"
    train_instance_path = engine.config['DataLoader']['Train']['dataset']['cls_label_path']
    val_instance_path = engine.config['DataLoader']['Eval']['dataset']['cls_label_path']
    class_names = engine.config['Global']['cls_name']
    train_size = engine.config['DataLoader']['Train']['dataset']['train_size']

    rank = -1
    with torch_distributed_zero_first(rank):
        train_datasets = LoadImagesAndLabelsInstanceAUG(train_instance_path, class_names, batch_size, imgsz,
                                    augment=True,  # augmentation
                                    hyp=engine.config,  # hyperparameters
                                    single_cls=False,
                                    prefix=colorstr('train: '),
                                    train_size = train_size,
                                    background_path = background_path)
    batch_size = min(batch_size, len(train_datasets))
    if sys.platform.startswith('linux'):
        nw = min([os.cpu_count() // WORLD_SIZE, batch_size if batch_size > 1 else 0, 8])  # number of workers
    else:
        nw = 0
    train_loader = DataLoaderX(train_datasets,
                                    batch_size=batch_size,
                                    shuffle=True,
                                    num_workers=nw,  # 部署时改为nw
                                    sampler=None,
                                    pin_memory=True,
                                    collate_fn=LoadImagesAndLabelsInstanceAUG.collate_fn)

    engine.train_datasets,  engine.train_loader = train_datasets , train_loader

    cls_id = {item:idx for idx, item in enumerate(class_names)}
    generate_and_save_val_data(background_folder=background_path, instance_path=val_instance_path,
                               hyp=engine.config, val_size=engine.config['DataLoader']['Eval']['dataset']['val_size'],
                               class_names=class_names, cls_id = cls_id, imgsz=imgsz, config=engine.config)
    engine.LOGGER.info(f"Generated {engine.config['DataLoader']['Eval']['dataset']['val_size']} validation images in {engine.config['Global']['val_path']}")

    engine.val_loader, engine.val_datasets = create_dataloader(f"{engine.config['Global']['val_path']}/val.txt",
                                             batch_size// WORLD_SIZE, gs, hyp=engine.config, augment=False,
                                             prefix=colorstr('val: '))

def build_dataLoader(engine):
    if engine.config["Global"]["update_mode"]:
        build_mobilenetV3_dataLoader(engine)
    else:
        build_yolov5_dataLoader(engine)
