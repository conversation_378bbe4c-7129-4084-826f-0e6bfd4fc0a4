import os
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import torch
import inspect
import json
from .CustomTransform import CustomTransform, LongEdgeResizeWithPadding
import random
from pytorch.data.preprocess import preprocess


def create_operators(params):
    assert isinstance(params, list), ('operator config should be a list')
    ops = []
    for operator in params:
        assert isinstance(operator, dict) and len(operator) == 1, "yaml format error"
        op_name = list(operator)[0]
        param = {} if operator[op_name] is None else operator[op_name]
        op = getattr(preprocess, op_name)(**param)
        ops.append(op)
    return ops


class CustomImageDataset(Dataset):
    def __init__(self, txt_file, config, mode= "train"):
        self.image_paths = []
        self.labels = []
        self.class_to_idx = {}
        self.config = config
        self.mode = mode
        self.train_size = config['DataLoader']['Train']['dataset']['train_size']
        self.val_size = config['DataLoader']['Eval']['dataset']['val_size']
        self.batch_size = config['Global']['batch_size']
        self.iterations_per_epoch = int(self.train_size / self.batch_size)

        if mode=="train":
            self.transform = create_operators(config['DataLoader']['Train']['dataset']['transform_ops'])
            # self.transform = transforms.Compose([transforms.RandomHorizontalFlip(),
            #                                      LongEdgeResizeWithPadding(config['Global']['image_size'][0]),
            #                                      CustomTransform(),
            #                                      transforms.ToTensor()])
        elif mode == "valid":
            self.transform = create_operators(config['DataLoader']['Eval']['dataset']['transform_ops'])
            # self.transform = transforms.Compose([transforms.RandomHorizontalFlip(),
            #                                      LongEdgeResizeWithPadding(config['Global']['image_size'][0]),
            #                                      transforms.ToTensor()])

        with open(f"{os.path.dirname(txt_file)}/subfolder_to_id.json", 'r') as file:
            self.class_to_idx = json.load(file)

        with open(txt_file) as file:
            lines = file.readlines()
            if (lines[0].strip().split()[1]).isnumeric():
                for l in lines:
                    l = l.strip().split()
                    self.image_paths.append(l[0])
                    self.labels.append(int(l[1]))
                assert os.path.exists(self.image_paths[-1])
            else:
                base_path = os.path.dirname(txt_file)
                cls_id = 0
                for l in lines:
                    l = l.strip().split()
                    self.image_paths.append(l[0])
                    clsName = l[1]
                    self.labels.append(int(self.class_to_idx[clsName]))
                assert os.path.exists(self.image_paths[-1])
        self.classes_len = int(max(self.labels))+1

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        image = Image.open(img_path)
        if isinstance(self.transform, list):
            for op in self.transform:
                image = op(image)
        else:
            image = self.transform(image)
        return image, label



class ForeverDataIterator:
    r"""A data iterator that will never stop producing data"""
    def __init__(self, data_loader: DataLoader):
        self.data_loader = data_loader
        self.iter = iter(self.data_loader)
    def __next__(self):
        try:
            data = next(self.iter)
        except StopIteration:
            self.iter = iter(self.data_loader)
            data = next(self.iter)
        return data
    def __len__(self):
        return len(self.data_loader)







class CheckScoreDataset(Dataset):
    def __init__(self, root_dir, samples_per_class=100, transform=None):
        self.root_dir = root_dir
        self.samples_per_class = samples_per_class
        self.transform = transform
        self.classes = sorted([item for item in os.listdir(root_dir) if os.path.isdir(f"{root_dir}/{item}")])  # 按字母顺序排序，确保一致性
        self.class_to_idx = {cls_name: idx for idx, cls_name in enumerate(self.classes)}
        self.data = []
        self.labels = []
        for cls_name in self.classes:
            cls_dir = os.path.join(root_dir, cls_name)
            all_images = [os.path.join(cls_dir, img) for img in os.listdir(cls_dir)
                          if img.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
            n_samples = min(len(all_images), self.samples_per_class)
            if n_samples == 0:
                raise ValueError(f"类别 {cls_name} 中没有图片文件！")
            sampled_images = random.sample(all_images, n_samples)
            self.data.extend(sampled_images)
            self.labels.extend([self.class_to_idx[cls_name]] * n_samples)
        assert len(self.data) == len(self.labels), "数据和标签长度不一致！"

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data[idx]
        label = self.labels[idx]
        try:
            img = Image.open(img_path)
        except Exception as e:
            raise RuntimeError(f"无法读取图片 {img_path}: {e}")
        if self.transform is not None:
            for op in self.transform:
                img = op(img)
        return img, label