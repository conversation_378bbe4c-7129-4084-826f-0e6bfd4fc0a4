import random
import cv2
import numpy as np
import torch
from PIL import Image
import os
import glob
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler, MaxAbsScaler, Normalizer
import torchvision.transforms as transforms
from pytorch.data.preprocess.functional import augmentations
from numpy import linalg as la


class AugMix(object):
    def __init__(self,  aug_prob_coeff=0.1,  proportion = 0.20 , pattern = "add"):
        self.aug_prob_coeff = aug_prob_coeff
        self.proportion = proportion
        self.augmentations = augmentations
        self.pattern = pattern

    def __call__(self, image):
        ws = np.float32(np.random.dirichlet([self.aug_prob_coeff] * 3))
        m = np.random.uniform(0, self.proportion)
        mix = np.zeros((*image.size,3))
        for i in range(3):
            image_aug = image.copy()
            # image_aug = Image.fromarray(image_aug.astype(np.uint8))
            op = np.random.choice(self.augmentations)
            image_aug = op(image_aug, 1)
            if self.pattern == "add":
                mix += ws[i] * np.asarray(image_aug)
            else:
                mix[:,:,i] = np.asarray(image_aug)[:,:,i]
        mixed = (1 - m) * np.asarray(image) + m * mix
        return mixed


class CustomTransform():
    def __init__(self):
        self.augmix = AugMix()

    def __call__(self, images):
        if random.random() < 0.3:
            select = random.choice(["Gamma_transf", "Augmix"])
            if "Gamma_transf" == select:
                images = np.array(images)
                shape = images.shape
                def apply_gamma_correction(image, gamma: float):
                    inv_gamma = 1.0 / gamma  # 计算逆向Gamma变换
                    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)], dtype=np.uint8)
                    return cv2.LUT(image.astype(np.uint8), table)
                images = apply_gamma_correction(images,gamma=random.uniform(0.90, 1.10))  # 示例：增加Gamma值来模拟不同的光照效果   # 增加亮度
                images = Image.fromarray(images.astype(np.uint8))
            if "Augmix" == select:
                images = self.augmix(images)
                images = Image.fromarray(images.astype(np.uint8))
        return images


class LongEdgeResizeWithPadding:
    def __init__(self, target_size):
        self.target_size = target_size

    def __call__(self, img):
        width, height = img.size
        if width > height:
            new_width = self.target_size
            new_height = int(self.target_size * height / width)
        else:
            new_height = self.target_size
            new_width = int(self.target_size * width / height)
        img = img.resize((new_width, new_height), Image.BILINEAR)
        new_img = Image.new("RGB", (self.target_size, self.target_size), (0, 0, 0))
        pad_left = (self.target_size - new_width) // 2
        pad_top = (self.target_size - new_height) // 2
        pad_right = self.target_size - new_width - pad_left
        pad_bottom = self.target_size - new_height - pad_top
        new_img.paste(img, (pad_left, pad_top))
        return new_img
