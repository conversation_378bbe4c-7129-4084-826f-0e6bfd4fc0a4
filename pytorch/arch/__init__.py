
# from . import backbone
from .backbone import *


# def build_model(config, mode="train"):
#     arch_config = copy.deepcopy(config["Arch"])
#     model_type = arch_config.pop("name")
#     use_sync_bn = arch_config.pop("use_sync_bn", False)
#     mod = importlib.import_module(__name__)
#     arch = getattr(mod, model_type)(**arch_config)
#     if use_sync_bn:
#         if config["Global"]["device"] == "gpu":
#             arch = nn.SyncBatchNorm.convert_sync_batchnorm(arch)
#         else:
#             msg = "SyncBatchNorm can only be used on GPU device. The releated setting has been ignored."
#             logger.warning(msg)
#
#     if isinstance(arch, TheseusLayer):
#         prune_model(config, arch)
#         quantize_model(config, arch, mode)
#
#     return arch
#
# class RecModel(TheseusLayer):
#     def __init__(self, **config):
#         super().__init__()
#         backbone_config = config["Backbone"]
#         backbone_name = backbone_config.pop("name")
#         self.backbone = eval(backbone_name)(**backbone_config)
#         if "BackboneStopLayer" in config:
#             backbone_stop_layer = config["BackboneStopLayer"]["name"]
#             self.backbone.stop_after(backbone_stop_layer)
#
#         if "Neck" in config:
#             self.neck = build_gear(config["Neck"])
#         else:
#             self.neck = None
#
#         if "Head" in config:
#             self.head = build_gear(config["Head"])
#         else:
#             self.head = None
#
#     def forward(self, x, label=None):
#         out = dict()
#         x = self.backbone(x)
#         out["backbone"] = x
#         if self.neck is not None:
#             x = self.neck(x)
#             out["neck"] = x
#         out["features"] = x
#         if self.head is not None:
#             y = self.head(x, label)
#             out["logits"] = y
#         return out



