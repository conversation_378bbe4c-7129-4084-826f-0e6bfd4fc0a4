# YOLOv5 🚀 by Ultralytics, GPL-3.0 license
import torch.nn as nn
# from pytorch.arch.backbone.yolov5.common import "Conv", "C3", "SPPF", "Concat"
# from pytorch.arch.backbone.yolov5.yolo import "Detect"



class YOLOBS():
    def __init__(self):
        super().__init__()
        self.nc = 80
        self.depth_multiple=1.0  # model depth multiple
        self.width_multiple=1.0  # layer channel multiple
        self.anchors=[[10,13, 16,30, 33,23],  # P3/8
                      [30,61, 62,45, 59,119],  # P4/16
                      [116,90, 156,198, 373,326]]  # P5/32

                     # [from, number, module, args]
        self.backbone= [[-1, 1, "Conv", [64, 6, 2, 2]],  # 0-P1/2
                       [-1, 1, "Conv", [128, 3, 2]],  # 1-P2/4
                       [-1, 3, "C3", [128]],
                       [-1, 1, "Conv", [256, 3, 2]],  # 3-P3/8
                       [-1, 6, "C3", [256]],
                       [-1, 1, "Conv", [512, 3, 2]],  # 5-P4/16
                       [-1, 9, "C3", [512]],
                       [-1, 1, "Conv", [1024, 3, 2]],  # 7-P5/32
                       [-1, 3, "C3", [1024]],
                       [-1, 1, "SPPF", [1024, 5]]]  # 9

        self.head=[[-1, 1, "Conv", [512, 1, 1]],
                   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
                   [[-1, 6], 1, "Concat", [1]],  # cat backbone P4
                   [-1, 3, "C3", [512, False]],  # 13

                   [-1, 1, "Conv", [256, 1, 1]],
                   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
                   [[-1, 4], 1, "Concat", [1]],  # cat backbone P3
                   [-1, 3, "C3", [256, False]],  # 17 (P3/8-small)

                   [-1, 1, "Conv", [256, 3, 2]],
                   [[-1, 14], 1, "Concat", [1]],  # cat head P4
                   [-1, 3, "C3", [512, False]],  # 20 (P4/16-medium)

                   [-1, 1, "Conv", [512, 3, 2]],
                   [[-1, 10], 1, "Concat", [1]],  # cat head P5
                   [-1, 3, "C3", [1024, False]],  # 23 (P5/32-large)

                   [[17, 20, 23], 1, "Detect", ["nc", "anchors"]]]  # "Detect"(P3, P4, P5)


class YOLOv5N(YOLOBS):
    def __init__(self):
        super().__init__()
        self.depth_multiple=0.33  # model depth multiple
        self.width_multiple=0.25  # layer channel multiple


class YOLOv5S(YOLOBS):
    def __init__(self):
        super().__init__()
        self.depth_multiple=0.33  # model depth multiple
        self.width_multiple=0.50  # layer channel multiple


class YOLOv5M(YOLOBS):
    def __init__(self):
        super().__init__()
        self.depth_multiple=0.67  # model depth multiple
        self.width_multiple=0.75  # layer channel multiple

class YOLOv5L(YOLOBS):
    def __init__(self):
        super().__init__()
        self.depth_multiple = 1.0  # model depth multiple
        self.width_multiple = 1.0  # layer channel multiple




class YOLOv5N_Relu_s4s8():
    def __init__(self):
        self.nc = 8
        self.depth_multiple=0.33  # model depth multiple
        self.width_multiple=0.25  # layer channel multiple
        self.activation = nn.ReLU()  # 使用ReLU激活函数
        self.anchors=[[5,6, 8,14],  # P3/4
                      [10,13, 16,30]]  # P3/8

                     # [from, number, module, args]
        self.backbone= [[-1, 1, "Conv", [64, 6, 2, 2]],  # 0-P1/2
                        [-1, 1, "Conv", [128, 3, 2]],  # 1-P2/4
                        [-1, 3, "C3", [128]],
                        [-1, 1, "Conv", [256, 3, 2]],  # 3-P3/8
                        [-1, 6, "C3", [256]],
                        [-1, 1, "SPPF", [256, 5]]]  # 9

        self.head=  [[-1, 1, "Conv", [128, 1, 1]],
                     [-1, 1, nn.Upsample, [None, 2, 'nearest']],
                     [[-1, 2], 1, "Concat", [1]],
                     [-1, 3, "C3", [128, False]],              #  (P2/4-small)
                     [-1, 1, "Conv", [128, 3, 2]],
                     [[-1, 6], 1, "Concat", [1]],
                     [-1, 3, "C3", [256, False]],             # (P3/8-medium)
                     [[9, 12], 1, "Detect", ["nc", "anchors"]],  # Detect(P2, P3)
                     ]


class YOLOv5N_Relu_8():
    def __init__(self):
        self.nc = 80
        self.depth_multiple = 0.33  # model depth multiple
        self.width_multiple = 0.25  # layer channel multiple
        self.activation = nn.ReLU()  # 使用ReLU激活函数
        self.anchors = [[16,30, 33,23]]  # P3/8

                        # [from, number, module, args]
        self.backbone = [[-1, 1, "Conv", [64, 3, 2]],  # 0-P1/2
                        [-1, 1, "Conv", [128, 3, 2]],  # 1-P2/4
                        [-1, 3, "C3", [128]],
                        [-1, 1, "Conv", [256, 3, 2]],  # 3-P3/8
                        [-1, 1, "C3", [256]],
                        [-1, 1, "Conv", [512, 3, 1]],  # 5-P4/8
                        [-1, 3, "C3", [512]],
                        [-1, 1, "SPPF", [512, 5]],  # 7
                        ]

        self.head = [[[-1, 6], 1, "Concat", [1]],  # cat backbone P3
                    [-1, 3, "C3", [256, False]],  # 9 (P3/8-small)
                    [[9], 1, "Detect", ["nc", "anchors"]],  # Detect(P3)
                    ]


class YOLOv5N_Relu_16():
    def __init__(self):
        self.nc = 1
        self.depth_multiple=0.33  # model depth multiple
        self.width_multiple=0.25  # layer channel multiple
        self.activation = nn.ReLU()  # 使用ReLU激活函数
        self.anchors=[[30,61, 62,45, 59,119]]  # P1/16

                        # [from, number, module, args]
        self.backbone=   [[-1, 1, "Conv", [64, 3, 2]],  # 0-P1/2
                           [-1, 1, "Conv", [128, 3, 2]],  # 1-P2/4
                           [-1, 3, "C3", [128]],
                           [-1, 1, "Conv", [256, 3, 2]],  # 3-P3/8
                           [-1, 1, "C3", [256]],
                           [-1, 1, "Conv", [512, 3, 2]],  # 5-P4/16
                           [-1, 3, "C3", [512]],
                           [-1, 1, "SPPF", [512, 5]],  # 7
                          ]

        self.head=  [[[-1, 6], 1, "Concat", [1]],  # cat backbone P4
                       [-1, 3, "C3", [256, False]],  # 9 (P4/16-medium)
                       [[9], 1, "Detect", ["nc", "anchors"]],  # Detect(P4)
                      ]


class YOLOv5N_Relu_8_16():
    def __init__(self):
        self.nc = 80
        self.depth_multiple = 0.33  # model depth multiple
        self.width_multiple = 0.25  # layer channel multiple
        self.activation = nn.ReLU()  # 使用ReLU激活函数
        self.anchors = [[16,30, 33,23],  # P3/8
                        [62,45, 59,119]]  # P4/16

                        # [from, number, module, args]
        self.backbone = [[-1, 1, "Conv", [64, 3, 2]],  # 0-P1/2
                        [-1, 1, "Conv", [128, 3, 2]],  # 1-P2/4
                        [-1, 3, "C3", [128]],
                        [-1, 1, "Conv", [256, 3, 2]],  # 3-P3/8
                        [-1, 6, "C3", [256]],
                        [-1, 1, "Conv", [512, 3, 2]],  # 5-P4/16
                        [-1, 9, "C3", [512]],
                        [-1, 1, "Conv", [1024, 3, 2]],  # 7-P5/32
                        [-1, 3, "C3", [1024]],
                        [-1, 1, "SPPF", [1024, 5]],  # 9
                        ]

        self.head = [[-1, 1, "Conv", [512, 1, 1]],
                    [-1, 1, nn.Upsample, [None, 2, 'nearest']],
                    [[-1, 6], 1, "Concat", [1]],  # cat backbone P4
                    [-1, 3, "C3", [512, False]],  # 13

                    [-1, 1, "Conv", [256, 1, 1]],
                    [-1, 1, nn.Upsample, [None, 2, 'nearest']],
                    [[-1, 4], 1, "Concat", [1]],  # cat backbone P3
                    [-1, 3, "C3", [256, False]],  # 17 (P3/8-small)

                    [-1, 1, "Conv", [256, 3, 2]],
                    [[-1, 14], 1, "Concat", [1]],  # cat head P4
                    [-1, 3, "C3", [512, False]],  # 20 (P4/16-medium)

                    [[17, 20], 1, "Detect", ["nc", "anchors"]],  # Detect(P3, P4)
                    ]


