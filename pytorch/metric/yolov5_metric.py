import os.path

import torch
import numpy as np
import datetime
from pathlib import Path
import cv2
import matplotlib
import matplotlib.pyplot as plt
import pandas as pd
import psutil
from copy import deepcopy
from pytorch.utils.general import xywh2xyxy, xyxy2xywh, box_iou, non_max_suppression

class YOLOv5_METRIC():
    def __init__(self, config, model):
        self.config = config
        self.device = next(model.parameters()).device
        self.seen = 0
        self.stats, self.ap_class = [], []
        self.iouv = torch.linspace(0.5, 0.95, 10).to(self.device)  # iou vector for mAP@0.5:0.95
        self.niou = self.iouv.numel()
        self.names = self.config['Global']['cls_name']
        self.epoch = 0
        self.epoch_acc = 0
        self.best_acc = 0.0
        self.image_size = config['Global']['image_size']
        self.time_info = {"epoch_cost": 0}
        self.keys = ['train/box_loss', 'train/obj_loss', 'train/cls_loss',  # train loss
                     'metrics/precision', 'metrics/recall', 'metrics/mAP_0.5', 'metrics/mAP_0.5:0.95']
        self.mloss = {"box_loss":0.0, "obj_loss":0.0, "cls_loss":0.0}
        self.mp, self.mr, self.map50,  self.map = 0.0, 0.0, 0.0, 0.0
        self.best_map50, self.best_map = 0.0, 0.0
        self.stats = []

    def reset(self):
        pass

    def update_loss(self, loss_items):
        with torch.no_grad():
            for key in self.mloss.keys():
                self.mloss[key] += float(loss_items[key])

    def calculation_loss(self, epoch, iters, LOGGER):
        self.epoch = epoch
        for key, value in self.mloss.items():
            self.mloss[key] = self.mloss[key] / iters
        LOGGER.info(('\n' + '%10s' * 8) % (f'Epoch', 'gpu_mem', 'box', 'obj', 'cls', 'img_h', 'img_w', 'RAM'))
        mem = f'{torch.cuda.memory_reserved() / 1E9 if torch.cuda.is_available() else 0:.3g}G'
        LOGGER.info(('%10s' * 2 + '%10.4g' * 5+ '%10s') % (f'{epoch+1}/{self.config["Global"]["epochs"]}',
                    mem, self.mloss["box_loss"], self.mloss["obj_loss"], self.mloss["cls_loss"],
                    self.config["Global"]["image_size"][0], self.config["Global"]["image_size"][1],
                    f"{psutil.virtual_memory().percent}%"))

    def process_batch(self, detections, labels, iouv):
        correct = torch.zeros(detections.shape[0], iouv.shape[0], dtype=torch.bool, device=iouv.device)
        iou = box_iou(labels[:, 1:], detections[:, :4])
        x = torch.where(
            (iou >= iouv[0]) & (labels[:, 0:1] == detections[:, 5]))  # IoU above threshold and classes match
        if x[0].shape[0]:
            matches = torch.cat((torch.stack(x, 1), iou[x[0], x[1]][:, None]),
                                1).cpu().numpy()  # [label, detection, iou]
            if x[0].shape[0] > 1:
                matches = matches[matches[:, 2].argsort()[::-1]]
                matches = matches[np.unique(matches[:, 1], return_index=True)[1]]
                # matches = matches[matches[:, 2].argsort()[::-1]]
                matches = matches[np.unique(matches[:, 0], return_index=True)[1]]
            matches = torch.Tensor(matches).to(iouv.device)
            correct[matches[:, 1].long()] = matches[:, 2:3] >= iouv
        return correct


    def compute_ap(self, recall, precision):
        mrec = np.concatenate(([0.0], recall, [1.0]))
        mpre = np.concatenate(([1.0], precision, [0.0]))
        # Compute the precision envelope
        mpre = np.flip(np.maximum.accumulate(np.flip(mpre)))
        # Integrate area under curve
        method = 'interp'  # methods: 'continuous', 'interp'
        if method == 'interp':
            x = np.linspace(0, 1, 101)  # 101-point interp (COCO)
            ap = np.trapz(np.interp(x, mrec, mpre), x)  # integrate
        else:  # 'continuous'
            i = np.where(mrec[1:] != mrec[:-1])[0]  # points where x axis (recall) changes
            ap = np.sum((mrec[i + 1] - mrec[i]) * mpre[i + 1])  # area under curve
        return ap, mpre, mrec


    def ap_per_class(self, tp, conf, pred_cls, target_cls,  names=(), eps=1e-16):
        i = np.argsort(-conf)
        tp, conf, pred_cls = tp[i], conf[i], pred_cls[i]

        # Find unique classes
        unique_classes, nt = np.unique(target_cls, return_counts=True)
        nc = unique_classes.shape[0]  # number of classes, number of detections

        # Create Precision-Recall curve and compute AP for each class
        px, py = np.linspace(0, 1, 1000), []  # for plotting
        ap, p, r = np.zeros((nc, tp.shape[1])), np.zeros((nc, 1000)), np.zeros((nc, 1000))
        for ci, c in enumerate(unique_classes):
            i = pred_cls == c
            n_l = nt[ci]  # number of labels
            n_p = i.sum()  # number of predictions

            if n_p == 0 or n_l == 0:
                continue
            else:
                # Accumulate FPs and TPs
                fpc = (1 - tp[i]).cumsum(0)
                tpc = tp[i].cumsum(0)

                # Recall
                recall = tpc / (n_l + eps)  # recall curve
                r[ci] = np.interp(-px, -conf[i], recall[:, 0], left=0)  # negative x, xp because xp decreases

                # Precision
                precision = tpc / (tpc + fpc)  # precision curve
                p[ci] = np.interp(-px, -conf[i], precision[:, 0], left=1)  # p at pr_score

                # AP from recall-precision curve
                for j in range(tp.shape[1]):
                    ap[ci, j], mpre, mrec = self.compute_ap(recall[:, j], precision[:, j])
                    if j == 0:
                        py.append(np.interp(px, mrec, mpre))  # precision at mAP@0.5

        # Compute F1 (harmonic mean of precision and recall)
        f1 = 2 * p * r / (p + r + eps)
        if isinstance(names, list):
            names = [v for k, v in enumerate(names) if k in unique_classes]
        else:
            names = [v for k, v in names.items() if k in unique_classes]  # list: only classes that have data
        names = {i: v for i, v in enumerate(names)}  # to dict

        i = f1.mean(0).argmax()  # max F1 index
        p, r, f1 = p[:, i], r[:, i], f1[:, i]
        tp = (r * nt).round()  # true positives
        fp = (tp / (p + eps) - tp).round()  # false positives
        return tp, fp, p, r, f1, ap, unique_classes.astype('int32')


    def update_metric(self, outputs, targets):
        # NMS
        height, width = self.image_size
        targets[:, 2:] *= torch.Tensor([width, height, width, height]).to(self.device)  # to pixels
        outputs = non_max_suppression(outputs, conf_thres=0.001, iou_thres=0.6, labels=[], multi_label=True, agnostic=False)

        for si, pred in enumerate(outputs):
            labels = targets[targets[:, 0] == si, 1:]
            nl = len(labels)
            tcls = labels[:, 0].tolist() if nl else []  # target class
            self.seen += 1
            if len(pred) == 0:
                if nl:
                    self.stats.append((torch.zeros(0, self.niou, dtype=torch.bool), torch.Tensor(), torch.Tensor(), tcls))
                continue
            predn = pred.clone()
            if nl:
                tbox = xywh2xyxy(labels[:, 1:5])  # target boxes
                labelsn = torch.cat((labels[:, 0:1], tbox), 1)  # native-space labels
                correct = self.process_batch(predn, labelsn, self.iouv)
            else:
                correct = torch.zeros(pred.shape[0], self.niou, dtype=torch.bool)
            self.stats.append((correct.cpu(), pred[:, 4].cpu(), pred[:, 5].cpu(), tcls))  # (correct, conf, pcls, tcls)

    def info_time(self, epoch, LOGGER):
        eta_sec = (self.config["Global"]["epochs"] - epoch) * self.time_info["epoch_cost"]
        eta_msg = "eta: {:s}".format(str(datetime.timedelta(seconds=int(eta_sec))))
        LOGGER.info(eta_msg)

    def calculation(self, epoch, LOGGER):    # Compute metrics
        self.epoch = epoch
        # LOGGER.info(('\n' + '%10s' * 1 + '%11s' * 3) % (f'Epoch', 'gpu_mem', 'img_size_h', 'img_size_w'))
        # mem = f'{torch.cuda.memory_reserved() / 1E9 if torch.cuda.is_available() else 0:.3g}G'
        # LOGGER.info(('%10s' * 2 + '%10.4g' * 2) % (f'{epoch+1}/{self.config["Global"]["epochs"]}', mem, self.config["Global"]["image_size"][0], self.config["Global"]["image_size"][1]))
        LOGGER.info(('%20s' + '%11s' * 6) % ('Class', 'Images', 'Labels', 'P', 'R', 'mAP@.5', 'mAP@.5:.95'))
        dt, p, r, f1, self.mp, self.mr, self.map50, map = [0.0, 0.0, 0.0], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0
        self.stats = [np.concatenate(x, 0) for x in zip(*self.stats)]  # to numpy
        if len(self.stats) and self.stats[0].any():
            tp, fp, p, r, f1, ap, self.ap_class = self.ap_per_class(*self.stats, names=list(self.names.keys()))
            ap50, ap = ap[:, 0], ap.mean(1)  # AP@0.5, AP@0.5:0.95
            self.mp, self.mr, self.map50, self.map = p.mean(), r.mean(), ap50.mean(), ap.mean()
            nt = np.bincount(self.stats[3].astype(np.int64), minlength=len(self.names))  # number of targets per class
        else:
            nt = torch.zeros(1)
        # Print results
        pf = '%20s' + '%11i' * 2 + '%11.3g' * 4  # print format
        LOGGER.info(pf % ('all', self.seen, nt.sum(), self.mp, self.mr, self.map50, self.map))
        # Print results per class
        if len(self.stats):
            for i, c in enumerate(self.ap_class):
                LOGGER.info(pf % (list(self.names.keys())[c], self.seen, nt[c], p[i], r[i], ap50[i], ap[i]))
        self.epoch_acc = 0.1 * self.map50 + 0.9 * self.map
        self.seen = 0
        self.stats = []

    def compare_and_save(self, model, model_dir, LOGGER):
        def safe_model_save(model, save_path):
            """安全保存模型，处理 torch.compile 的情况"""
            try:
                # 尝试直接保存
                if hasattr(model, '_orig_mod'):
                    # 编译后的模型，保存原始模型
                    ckpt = {'model': deepcopy(model._orig_mod).half()}
                else:
                    # 未编译的模型
                    ckpt = {'model': deepcopy(model).half()}
                torch.save(ckpt, save_path)
                print(f"模型已保存到: {save_path}")
            except Exception as e:
                print(f"保存整个模型失败: {e}")
                # 回退方案：只保存权重
                ckpt = {'model': model.state_dict()}
                torch.save(ckpt, save_path)
                print(f"已保存模型权重到: {save_path}")
        if self.epoch_acc > self.best_acc:
            self.best_acc = self.epoch_acc
            self.best_map50 =  self.map50
            self.best_map = self.map
            # ckpt = {'ema': deepcopy(model).half()}
            # torch.save(ckpt, f"{model_dir}/best_model.pt")  # 最好权重
            safe_model_save(model, f"{model_dir}/best_model.pt")
            LOGGER.info(f'Save Best model in path {model_dir}/best_model.pt')
        vals = [self.mloss["box_loss"], self.mloss["obj_loss"], self.mloss["cls_loss"],
               self.mp, self.mr, self.map50, self.map]
        file = Path(f"{self.config['Global']['output_dir']}/RecModel/results.csv")
        n = len(self.keys) + 1  # number of cols
        s = '' if file.exists() else (('%20s,' * n % tuple(['epoch'] + self.keys)).rstrip(',') + '\n')  # add header
        with open(file, 'a') as f:
            f.write(s + ('%20.5g,' * n % tuple([self.epoch] + vals)).rstrip(',') + '\n')

    def placeholder(self,):
        pass

    def plot_results(self, file='path/to/results.csv', dir=''):
        save_dir = Path(file).parent if file else Path(dir)
        fig, ax = plt.subplots(2, 5, figsize=(12, 6), tight_layout=True)
        ax = ax.ravel()
        files = list(save_dir.glob('results*.csv'))
        assert len(files), f'No results.csv files found in {save_dir.resolve()}, nothing to plot.'
        for fi, f in enumerate(files):
            try:
                data = pd.read_csv(f)
                s = [x.strip() for x in data.columns]
                x = data.values[:, 0]
                for i, j in enumerate([1, 2, 3, 4, 5, 8, 9, 10, 6, 7]):
                    y = data.values[:, j]
                    ax[i].plot(x, y, marker='.', label=f.stem, linewidth=2, markersize=8)
                    ax[i].set_title(s[j], fontsize=12)
            except Exception as e:
                print(f'Warning: Plotting error for {f}: {e}')
        ax[1].legend()
        fig.savefig(save_dir / 'results.png', dpi=200)
        plt.close()

    def output_detect_label(self, config, outputs, targets, path):
        testPath = config['DataLoader']['Test']['dataset']['image_root']
        LabelPath = f"{testPath}/label_pred"
        if not os.path.exists(LabelPath):
            os.mkdir(LabelPath)
        height, width = self.image_size
        targets[:, 2:] *= torch.Tensor([width, height, width, height]).to(self.device)  # to pixels
        outputs = non_max_suppression(outputs, conf_thres=0.1, iou_thres=0.5, labels=[], multi_label=True, agnostic=False)

        for i, det in enumerate(outputs):  # per image
            p = path[i]
            gn = torch.tensor(self.image_size)[[1, 0, 1, 0]]  # normalization gain whwh
            p = Path(p)  # to Path
            Name = str(p.name).split(".")[0]
            txt_path = f"{LabelPath}/{Name}.txt"
            if len(det):
                for *xyxy, conf, cls in reversed(det):
                    xywh = (xyxy2xywh(torch.tensor(xyxy).view(1, 4)) / gn).view(-1).tolist()    # normalized xywh
                    line = (cls, *xywh, conf)  # label format
                    with open(txt_path, 'a') as f:
                        f.write(('%g ' * len(line)).rstrip() % line + '\n')
                    f.close()
            else:
                with open(txt_path, 'a') as f:
                    f.write('\n')
                f.close()

