import torch
from .yolov5_metric import YOLOv5_METRIC
import datetime

class METRIC():
    def __init__(self, config, model):
        self.config = config
        self.running_loss = {}
        self.correct = 0.0
        self.total = 0.0
        self.epoch_loss = {}
        self.epoch_acc = 0
        self.best_acc = 0.0
        self.val = False
        self.time_info = {"epoch_cost":0}

    def reset(self):
        self.running_loss, self.epoch_loss = {}, {}
        self.correct, self.total,  self.epoch_acc = 0.0, 0.0, 0.0
        self.val = True

    def update_metric(self, outputs, labels):
        outputs = outputs[1] if type(outputs) is tuple else outputs
        _, predicted = torch.max(outputs, 1)
        self.total += labels.size(0)
        self.correct += (predicted == labels).sum().item()

    def update_loss(self, loss_item=None):
        for key , value in loss_item.items():
            if key not in self.running_loss:
                self.running_loss[key] = 0
            self.running_loss[key] += value

    def info_time(self, epoch, LOGGER):
        eta_sec = (self.config["Global"]["epochs"] - epoch + 1) * self.time_info["epoch_cost"]
        eta_msg = "eta: {:s}".format(str(datetime.timedelta(seconds=int(eta_sec))))
        LOGGER.info(eta_msg)
        
    def calculation(self, epoch, LOGGER):
        self.epoch_acc = self.correct / self.total
        if not self.val:
            for key, value in self.running_loss.items():
                self.epoch_loss[key] = self.running_loss[key] / self.total
            LOGGER.info(f'Epoch {epoch + 1}/{self.config["Global"]["epochs"]}, Training Loss: {self.epoch_loss}, Training Accuracy: {self.epoch_acc:.4f}')
            # LOGGER.info(f'Epoch {epoch + 1}/{self.config["Global"]["epochs"]}, Training Loss: {self.epoch_loss}')
        else:
            LOGGER.info(f'Val Accuracy: {self.epoch_acc:.4f}, Best Accuracy: {self.best_acc:.4f}')
        self.val = False

    def compare_and_save(self, model, model_dir, LOGGER):
        if self.epoch_acc > self.best_acc:
            self.best_acc = self.epoch_acc
            self.best_model = model
            torch.save(model.state_dict(), f"{model_dir}/best_model.pth")  # 最好权重
            LOGGER.info(f'Save Best model in path {model_dir}/best_model.pth')
            return self.best_model
        else:
            return self.best_model
    def placeholder(self,):
        pass


def build_mobilenetV3_metric(engine):
    engine.metric = METRIC(engine.config, engine.model)


def build_yolov5_metric(engine):
    engine.metric = YOLOv5_METRIC(engine.config, engine.model)


def build_metric(engine):
    if engine.config["Global"]["update_mode"]:
        build_mobilenetV3_metric(engine)
    else:
        build_yolov5_metric(engine)

