from torch.nn import Module
import torch
from torch import nn


class CustomHardswish(Module):
    __constants__ = ['inplace']
    inplace: bool
    def __init__(self, inplace : bool = False) -> None:
        super().__init__()
        self.inplace = inplace

    def forward(self, input):
        return input*torch.clamp(input+3, min=0,max=6)/6


class CustomHardsigmoid(Module):
    __constants__ = ['inplace']
    inplace: bool
    def __init__(self, inplace : bool = False)->None:
        super().__init__()
        self.inplace = inplace
    def forward(self, input, inplace = False):
        return torch.clamp((input*0.167+0.5), 0, 1)


def find_ops(parent):
    replace_mods = []
    if parent is None:
        return []
    for name, child in parent.named_children():
        if isinstance(child, nn.ReLU):
           module = nn.ReLU6()
           replace_mods.append((parent, name, module))
        if isinstance(child, nn.Hardswish):
            module = CustomHardswish()
            replace_mods.append((parent, name, module))
        elif isinstance(child, nn.Hardsigmoid):
            module = CustomHardsigmoid()
            replace_mods.append((parent, name, module))
        else:
            replace_mods.extend(find_ops(child))
    return replace_mods


def chang_model_act(model):
    replace_mods = find_ops(model)
    for (parent, name, child) in replace_mods:
        setattr(parent, name, child)
    return  model