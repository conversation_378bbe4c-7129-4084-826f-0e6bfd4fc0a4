import time
from torchvision import datasets, models, transforms
from torch.utils.data import Data<PERSON>oader
import os
import torch
import numpy as np
from pytorch.arch.backbone.legendary_models.mobilenet_v3 import new_forward_impl
from tqdm import tqdm
from pytorch.data.dataloader.CustomTransform import LongEdgeResizeWithPadding
from pytorch.data.dataloader import *
from pytorch.utils.general import cal_score
from pytorch.configs.config import parse_config
import random


def check_score(engine):
    ######################### 固定随机种子 ###########################
    random.seed(0)
    np.random.seed(0)
    torch.manual_seed(0)
    torch.cuda.manual_seed_all(0)
    ######################### 固定随机种子 ###########################

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    width_mult = 1.0
    retrival_model = models.mobilenet_v3_large(weights=None, width_mult=width_mult).to(device)
    model_pattern = "_".join(str(width_mult).split("."))
    model_weight_path = f"{engine.config['Global']['pretrained_model']}/best_model_MobileNetV3_large_x{model_pattern}.pth"
    if os.path.exists(model_weight_path):
        pre_weights = torch.load(model_weight_path)
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in retrival_model.state_dict() and v.shape == retrival_model.state_dict()[k].shape}
        missing_keys, unexpected_keys = retrival_model.load_state_dict(pre_dict, strict=False)
    else:
        engine.config['Global']['epochs'] = 60
    retrival_model._forward_impl = new_forward_impl.__get__(retrival_model, models.MobileNetV3)

    transform = create_operators(engine.config['DataLoader']['Eval']['dataset']['transform_ops'])
    baseFolderspath = engine.config['DataLoader']['Eval']['dataset']['image_root']
    check_score_dataset = CheckScoreDataset(baseFolderspath, samples_per_class=100, transform=transform)
    check_score_dataloader = DataLoader(check_score_dataset, batch_size=4, shuffle=True)
    outputs = []
    labels = []
    with torch.no_grad():
        for input, label in check_score_dataloader:
            input =input.to(device)
            out = retrival_model(input)[0]
            out = torch.divide(out, torch.norm(out, p=2, dim=-1, keepdim=True)).detach().cpu().numpy()
            outputs.append(out)
            labels.append(label.numpy()[:,None])
        outputs = np.vstack(outputs)
        labels = np.vstack(labels).ravel()

    difficulty = cal_score(engine.config, outputs, labels, engine.LOGGER)

    org_config = parse_config(os.path.join(engine.config['Global']['project_root'], 'configs/config.yaml'))
    
    if "epochs" not in org_config.keys():
        if difficulty == "easy":
            engine.config['Global']['epochs'] = 30
        elif difficulty == "middle":
            engine.config['Global']['epochs'] = 60
        elif difficulty == "hard":
            engine.config['Global']['epochs'] = 100

    del retrival_model
    torch.cuda.empty_cache()