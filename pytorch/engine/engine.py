from torchvision import datasets, transforms, models
import timm
import os
import torch
import math
from copy import deepcopy
from pytorch.engine.CustomOps import chang_model_act
from pytorch.arch.backbone.yolov5.yolo import Model
from pytorch.arch.backbone.legendary_models.mobilenet_v3 import new_forward_impl
try:
    from torch.amp import GradScaler, autocast
except:
    from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
import torch
import torch.nn as nn
import numpy as np
import torch.optim as optim
import time
import sys
import datetime
from itertools import islice
from pytorch.data.dataloader.create_dataloader import LoadImagesAndLabelsInstanceAUG, InfiniteDataLoader
from pytorch.data.dataloader.speed_dataloader import optimized_epoch_batch 
from pytorch.utils.general import torch_distributed_zero_first
from pytorch.utils.general import init_seeds
from torch.profiler import profile, tensorboard_trace_handler, schedule



def build_mobilenetV3_large_model(engine):
    width_mult = 0.35 if engine.config['Arch']['Backbone']['name']=='MobileNetV3_large_x0_35' else 1.0
    model = models.mobilenet_v3_large(pretrained=False, width_mult=width_mult)
    num_ftrs = model.classifier[3].in_features
    model.classifier[3] = nn.Linear(num_ftrs, engine.config['Arch']['Head']['class_num'])  # 替换最后的分类层，使其适应你的类别数
    ##################### pre weights #####################
    model_pattern = "_".join(str(width_mult).split("."))
    model_weight_path = f"{engine.config['Global']['pretrained_model']}/MobileNetV3_large_x{model_pattern}.pth"
    if os.path.exists(model_weight_path):
        pre_weights = torch.load(model_weight_path)
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in model.state_dict() and v.shape == model.state_dict()[k].shape}
        missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
        engine.LOGGER.info(f"len(pre_dict): {len(pre_dict.keys())}")
        engine.LOGGER.info(f"missing_keys: {missing_keys}")
    ##################### 替换 Hardswish, Hardsigmoid 算子 #####################
    if engine.config['Global']['platform'] == 'gaotong':
        model = chang_model_act(model)

    ###################### 如果是retrieval，替换forward函数增加输出项 #####################
    if engine.config['Global']['eval_mode'] == 'retrieval':
        model._forward_impl = new_forward_impl.__get__(model, models.MobileNetV3)  # 修改model._forward_impl为新的函数

    engine.model = model

def build_mobilenetV4_conv_model(engine):
    model = timm.create_model('mobilenetv4_conv_small', pretrained=False, num_classes=1000)  # 使用动态获取的类别数
    model.conv_head = nn.Conv2d(960, 128, kernel_size=1, stride=1, padding=0, bias=False)
    model.norm_head = nn.BatchNorm2d(128)
    model.classifier = nn.Linear(128, engine.config['Arch']['Head']['class_num'])   # 替换最后的分类层，使其适应你的类别数
    ##################### pre weights #####################
    model_weight_path = f"{engine.config['Global']['pretrained_model']}/mobilenetv4_conv_small.pth"
    if os.path.exists(model_weight_path):
        pre_weights = torch.load(model_weight_path)
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in model.state_dict() and v.shape == model.state_dict()[k].shape}
        missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
        engine.LOGGER.info(f"len(pre_dict): {len(pre_dict.keys())}")
        engine.LOGGER.info(f"missing_keys: {missing_keys}")
    ##################### 替换 Hardswish, Hardsigmoid 算子 #####################
    if engine.config['Global']['platform'] == 'gaotong':
        model = chang_model_act(model)
    engine.model = model


def build_yolov5_model(engine):
    model = Model(engine.config, engine.LOGGER, ch=3, nc=engine.config['Arch']['Head']['class_num'])
    model_weight_path = f"{engine.config['Global']['pretrained_model']}/{engine.config['Arch']['Backbone']['name']}.pth"
    if os.path.exists(model_weight_path):
        pre_weights = torch.load(model_weight_path)
        anchor_grid = model._modules['model']._modules['24'].anchor_grid
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in model.state_dict() and v.shape == model.state_dict()[k].shape}
        missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
        model._modules['model']._modules['24'].anchor_grid = anchor_grid
        engine.LOGGER.info(f"len(pre_dict): {len(pre_dict.keys())}")
        engine.LOGGER.info(f"missing_keys: {missing_keys}")
    # ###################### 替换 relu, Hardswish, Hardsigmoid 算子 #####################
    # if engine.config['Global']['platform']=='gaotong':
    #     model = chang_model_act(model)
    engine.model = model

def build_model(engine):
    if 'MobileNetV3' in engine.config['Arch']['Backbone']['name']:
        build_mobilenetV3_large_model(engine)
    elif 'MobileNetV4' in engine.config['Arch']['Backbone']['name']:
        build_mobilenetV4_conv_model(engine)
    elif 'YOLOv5' in engine.config['Arch']['Backbone']['name']:
        build_yolov5_model(engine)


class ModelEMA:
    def __init__(self, model, decay=0.9999, updates=0):
        self.ema = deepcopy(model.module if self.is_parallel(model) else model).eval()  # FP32 EMA
        self.updates = updates  # number of EMA updates
        self.decay = lambda x: decay * (1 - math.exp(-x / 2000))  # decay exponential ramp (to help early epochs)
        for p in self.ema.parameters():
            p.requires_grad_(False)

    def is_parallel(self, model):
        return type(model) in (nn.parallel.DataParallel, nn.parallel.DistributedDataParallel)

    def update(self, model):
        with torch.no_grad():
            self.updates += 1
            d = self.decay(self.updates)
            msd = model.module.state_dict() if self.is_parallel(model) else model.state_dict()  # model state_dict
            for k, v in self.ema.state_dict().items():
                if v.dtype.is_floating_point:
                    v *= d
                    v += (1 - d) * msd[k].detach()

    def update_attr(self, model, include=(), exclude=('process_group', 'reducer')):
        self.copy_attr(self.ema, model, include, exclude)  # Update EMA attributes

    def copy_attr(self, a, b, include=(), exclude=()):
        for k, v in b.__dict__.items():
            if (len(include) and k not in include) or k.startswith('_') or k in exclude:
                continue
            else:
                setattr(a, k, v)

def clip_weights(model, clip_value=1.0):
    for name, module in model.named_modules():
        if isinstance(module, nn.Conv2d):  # 检测DW卷积
            weight_median = float(torch.median(module.weight.data.detach().cpu()))
            if module.groups == module.in_channels:
                module.weight.data.clamp_(weight_median-0.2, weight_median+0.2)
            else:
                module.weight.data.clamp_(weight_median-clip_value, weight_median+clip_value)
        if isinstance(module, nn.BatchNorm2d):
            weight_median = float(torch.median(module.weight.data.detach().cpu()))
            module.weight.data.clamp_(weight_median-0.5, weight_median+0.5)
            if module.bias is not None:
                bias_median = float(torch.median(module.bias.data.detach().cpu()))
                module.bias.data.clamp_(bias_median-clip_value, bias_median+clip_value) 

class ENGINE:
    def __init__(self):
        self.config = None
        self.LOGGER = None
        self.train_loader = None
        self.val_loader = None
        self.train_datasets = None
        self.val_datasets = None
        self.device = None
        self.model = None
        self.ema = None
        self.criterion = None
        self.optimizer = None
        self.scheduler = None
        self.metric = None
        self.state = {}
        self.instance_gallery = None
        self.background_img = None
        self.instance_img_gallery = None

    def update_weight(self):
        if self.config["Global"]["platform"] == 'gaotong':
            # 高通平台：加载.pth文件
            if os.path.exists(f"{self.config['Global']['model_dir']}/best_model.pth"):
                weight_path = f"{self.config['Global']['model_dir']}/best_model.pth"
                model_weights = torch.load(weight_path)
                self.model.load_state_dict(model_weights, strict=True)
        elif self.config["Global"]["platform"] == 'qingwei':
            # 清微平台：加载.pt文件
            if os.path.exists(f"{self.config['Global']['model_dir']}/best_model.pt"):
                weight_path = f"{self.config['Global']['model_dir']}/best_model.pt"
                checkpoint = torch.load(weight_path)
                # 检查是否是字典格式并包含'ema'或'model'键
                if 'ema' in checkpoint:
                    self.model = checkpoint['ema']
                elif 'model' in checkpoint:
                    self.model = checkpoint['model']
                self.model = self.model.to(self.device)

    
    def train(self):
        if self.config["Global"]["update_mode"]:
            self.train_mobile_()
        else:
            self.train_yolo()

    def train_yolo_ema(self):
        model_dir = self.config['Global']['model_dir']
        self.ema = ModelEMA(self.model)
        nw = max(round(3 * len(self.train_loader)), 1000)
        num_epochs = self.config["Global"]["epochs"]
        train_size = self.config['DataLoader']['Train']['dataset']['train_size']
        imgsz = self.config['Global']['image_size']
        batch_size = self.config['Global']['batch_size']
        last_opt_step = -1
        self.scheduler.last_epoch = - 1  

        # self.model = torch.compile(self.model)

        fp_16 = self.config["Global"]["fp_16"]
        if fp_16:
            scaler = GradScaler()
        for epoch in range(num_epochs):
            tic = time.time()
            self.model.train()
            self.optimizer.zero_grad()
            #####################################20250702修改 by LYH ########################################################
            if epoch % 5 == 0:
                epoch_batch_start = time.time()
                epoch_batch = optimized_epoch_batch(self.train_datasets.instance_gallery, imgsz, num_batches=train_size, num_processes=8)
                self.train_datasets.epoch_batch = epoch_batch
                print(f"optimized_epoch_batch耗时:{time.time()-epoch_batch_start}秒")
            #################################################################################################################
            # with torch.profiler.profile(schedule=torch.profiler.schedule(wait=1, warmup=1, active=2, repeat=1),
            #                             on_trace_ready=torch.profiler.tensorboard_trace_handler("./logdir"),
            #                             record_shapes=False,
            #                             with_stack=False,
            #                             profile_memory=False,
            #                             with_flops=True
            #                         ) as prof: 
            for i, (inputs, labels) in tqdm(enumerate(self.train_loader), total=len(self.train_loader)):
                inputs, labels = inputs.to(self.device, non_blocking=True).float()/255, labels.to(self.device, non_blocking=True)
                # Warmup
                ni = i + len(self.train_loader) * epoch
                if ni <= nw:
                    xi = [0, nw]  # 插值范围
                    accumulate = max(1, np.interp(ni, xi, [1, 64 / batch_size]).round())  # 梯度累积
                    for j, x in enumerate(self.optimizer.param_groups):  # 调整学习率和动量
                        lf = ((1 - math.cos(epoch * math.pi / num_epochs)) / 2) * (0.1 - 1) + 1
                        x['lr'] = np.interp(ni, xi, [0.1 if j == 2 else 0.0, x['initial_lr'] * lf])
                        if 'momentum' in x:
                            x['momentum'] = np.interp(ni, xi, [0.8, 0.937])
                if fp_16:
                    with autocast('cuda'):
                        outputs = self.model(inputs)
                        loss, loss_item = self.criterion(outputs, labels)
                    scaler.scale(loss).backward()
                else:
                    outputs = self.model(inputs)
                    loss, loss_item = self.criterion(outputs, labels)
                    loss.backward()      
                if ni - last_opt_step >= accumulate:
                    if fp_16:
                        scaler.step(self.optimizer) # 更新权重
                        scaler.update()
                    else:
                        self.optimizer.step()  # 更新权重
                    self.optimizer.zero_grad()
                    self.ema.update(self.model)
                    last_opt_step = ni   
                self.metric.update_loss(loss_item)
                # prof.step()
            self.scheduler.step()  # 更新学习率
            ###############20250424修改CLIP中值权重##################
            clip_weights(self.model, clip_value=1.0)
            self.ema.update_attr(self.model, include=['yaml', 'nc', 'config', 'names', 'stride', 'class_weights'])
            clip_weights(self.ema.ema, clip_value=1.0)
            ########################################################     
            self.metric.time_info["epoch_cost"] = time.time() - tic
            tic = time.time()
            self.metric.calculation_loss(epoch, len(self.train_loader), self.LOGGER)

            # 在验证集上评估模型
            if (epoch + 1) % self.config['Global']['eval_interval'] == 0 and epoch != 0:
                self.ema.ema.eval()
                self.metric.reset()
                with torch.no_grad():
                    for inputs, labels in self.val_loader:
                        inputs, labels = inputs.to(self.device), labels.to(self.device)
                        outputs = self.ema.ema(inputs)
                        self.metric.update_metric(outputs, labels)
                self.metric.calculation(epoch, self.LOGGER)
                self.metric.compare_and_save(self.ema.ema, model_dir, self.LOGGER)
            self.metric.info_time(epoch, self.LOGGER)

        weight_path = f"{model_dir}/last_model.pth"
        torch.save(self.ema.ema.state_dict(), weight_path)  # 最后一轮权重
        self.LOGGER.info(f"Save Last model in path {weight_path}")
        self.LOGGER.info(f'Finish Training')
        self.LOGGER.info(f'best metric: mAP@.5:{round(self.metric.best_map50*100,2)}, mAP@.5:.95:{round(self.metric.best_map*100,2)}')

    def train_yolo(self):
        model_dir = self.config['Global']['model_dir']
        nw = max(round(3 * len(self.train_loader)), 1000)
        num_epochs = self.config["Global"]["epochs"]
        train_size = self.config['DataLoader']['Train']['dataset']['train_size']
        imgsz = self.config['Global']['image_size']
        batch_size = self.config['Global']['batch_size']
        last_opt_step = -1
        self.scheduler.last_epoch = - 1  
        #########20250716修改 by LYH #########
        self.model = torch.compile(self.model, mode="reduce-overhead")
        ######################################
        fp_16 = self.config["Global"]["fp_16"]
        if fp_16:
            scaler = GradScaler()
        for epoch in range(num_epochs):
            tic = time.time()
            self.model.train()
            self.optimizer.zero_grad()
            #####################################20250702修改 by LYH ########################################################
            if epoch % 5 == 0:
                epoch_batch_start = time.time()
                epoch_batch = optimized_epoch_batch(self.train_datasets.instance_gallery, imgsz, num_batches=train_size, num_processes=8)
                self.train_datasets.epoch_batch = epoch_batch
                print(f"optimized_epoch_batch耗时:{time.time()-epoch_batch_start}秒")
            #################################################################################################################
            for i, (inputs, labels) in tqdm(enumerate(self.train_loader), total=len(self.train_loader)):
                inputs, labels = inputs.to(self.device, non_blocking=True).float()/255, labels.to(self.device, non_blocking=True)
                # Warmup
                ni = i + len(self.train_loader) * epoch
                if ni <= nw:
                    xi = [0, nw]  # 插值范围
                    accumulate = max(1, np.interp(ni, xi, [1, 64 / batch_size]).round())  # 梯度累积
                    for j, x in enumerate(self.optimizer.param_groups):  # 调整学习率和动量
                        lf = ((1 - math.cos(epoch * math.pi / num_epochs)) / 2) * (0.1 - 1) + 1
                        x['lr'] = np.interp(ni, xi, [0.1 if j == 2 else 0.0, x['initial_lr'] * lf])
                        if 'momentum' in x:
                            x['momentum'] = np.interp(ni, xi, [0.8, 0.937])
                if fp_16:
                    with autocast('cuda'):
                        outputs = self.model(inputs)
                        loss, loss_item = self.criterion(outputs, labels)
                    scaler.scale(loss).backward()
                else:
                    outputs = self.model(inputs)
                    loss, loss_item = self.criterion(outputs, labels)
                    loss.backward()      
                if ni - last_opt_step >= accumulate:
                    if fp_16:
                        scaler.step(self.optimizer) # 更新权重
                        scaler.update()
                    else:
                        self.optimizer.step()  # 更新权重
                    self.optimizer.zero_grad()
                    last_opt_step = ni   
                self.metric.update_loss(loss_item)
            self.scheduler.step()  # 更新学习率
            ###############20250424修改CLIP中值权重##################
            clip_weights(self.model, clip_value=1.0)
            ########################################################     
            self.metric.time_info["epoch_cost"] = time.time() - tic
            tic = time.time()
            self.metric.calculation_loss(epoch, len(self.train_loader), self.LOGGER)

            # 在验证集上评估模型
            if (epoch + 1) % self.config['Global']['eval_interval'] == 0 and epoch != 0:
                self.model.eval()
                self.metric.reset()
                with torch.no_grad():
                    for inputs, labels in self.val_loader:
                        inputs, labels = inputs.to(self.device), labels.to(self.device)
                        outputs = self.model(inputs)
                        self.metric.update_metric(outputs, labels)
                self.metric.calculation(epoch, self.LOGGER)
                self.metric.compare_and_save(self.model, model_dir, self.LOGGER)
            self.metric.info_time(epoch, self.LOGGER)

        weight_path = f"{model_dir}/last_model.pth"
        torch.save(self.model.state_dict(), weight_path)  # 最后一轮权重
        self.LOGGER.info(f"Save Last model in path {weight_path}")
        self.LOGGER.info(f'Finish Training')
        self.LOGGER.info(f'best metric: mAP@.5:{round(self.metric.best_map50*100,2)}, mAP@.5:.95:{round(self.metric.best_map*100,2)}')

    def train_mobile_(self):
        model_dir = self.config['Global']['model_dir']
        num_epochs = self.config["Global"]["epochs"]
        best_acc = 0.0  # 保存最佳准确率
        best_weights = None  # 保存最佳模型权重
        for epoch in range(num_epochs):
            tic = time.time()
            self.model.train()  # 设置为训练模式
            running_loss = 0.0
            correct = 0
            total = 0
            for i in tqdm(range(self.train_datasets.iterations_per_epoch), total=self.train_datasets.iterations_per_epoch):
                inputs, labels = next(self.train_loader)  # 直接调用 next()
                inputs, labels = inputs.to(self.device), labels.to(self.device)

                self.optimizer.zero_grad()  # 清除梯度
                outputs = self.model(inputs)  # 前向传播
                loss, _ = self.criterion(outputs, labels)  # 计算损失    使用softmax层的话，损失使用交叉熵损失可能得改？
                loss.backward()  # 反向传播
                self.optimizer.step()  # 更新权重

                running_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

            self.scheduler.step()  # 更新学习率

            # ###############20250417修改CLIP权重######################
            # def clip_weights(model, clip_value=1.0):
            #     for name, module in model.named_modules():
            #         if isinstance(module, nn.Conv2d):  # 检测DW卷积
            #             if module.groups == module.in_channels:
            #                 module.weight.data.clamp_(-0.2, 0.2)
            #             else:
            #                 module.weight.data.clamp_(-clip_value, clip_value)
            #         if isinstance(module, nn.BatchNorm2d):
            #             module.weight.data.clamp_(-clip_value, clip_value)
            #             if module.bias is not None:
            #                 module.bias.data.clamp_(-clip_value, clip_value)
            # clip_weights(self.model, clip_value=1.0)
            # ########################################################
            ###############20250424修改CLIP中值权重######################
            def clip_weights(model, clip_value=1.0):
                for name, module in model.named_modules():
                    if isinstance(module, nn.Conv2d):  # 检测DW卷积
                        weight_median = float(torch.median(module.weight.data.detach().cpu()))
                        if module.groups == module.in_channels:
                            module.weight.data.clamp_(weight_median-0.2, weight_median+0.2)
                        else:
                            module.weight.data.clamp_(weight_median-clip_value, weight_median+clip_value)
                    if isinstance(module, nn.BatchNorm2d):
                        weight_median = float(torch.median(module.weight.data.detach().cpu()))
                        module.weight.data.clamp_(weight_median-0.5, weight_median+0.5)
                        if module.bias is not None:
                            bias_median = float(torch.median(module.bias.data.detach().cpu()))
                            module.bias.data.clamp_(bias_median-clip_value, bias_median+clip_value)
            clip_weights(self.model, clip_value=1.0)
            ########################################################

            # 打印每个 epoch 的训练信息
            epoch_loss = running_loss / len(self.train_datasets)
            epoch_acc = correct / total
            self.LOGGER.info(f'Epoch {epoch + 1}/{num_epochs}, Training Loss: {epoch_loss:.4f}, Training Accuracy: {epoch_acc:.4f}')

            # 在验证集上评估模型
            self.model.eval()  # 设置为评估模式
            val_loss = 0.0
            correct = 0
            total = 0
            with torch.no_grad():
                for inputs, labels in self.val_loader:
                    inputs, labels = inputs.to(self.device), labels.to(self.device)
                    outputs = self.model(inputs)
                    loss, _ = self.criterion(outputs, labels)
                    val_loss += loss.item() * inputs.size(0)
                    _, predicted = torch.max(outputs, 1)
                    total += labels.size(0)
                    correct += (predicted == labels).sum().item()

            val_loss = val_loss / len(self.val_datasets)
            val_acc = correct / total
            self.LOGGER.info(f'Validation Loss: {val_loss:.4f}, Validation Accuracy: {val_acc:.4f}, best_acc: {best_acc:.4f}')

            epoch_cost = time.time() - tic
            tic = time.time()
            eta_sec = (self.config["Global"]["epochs"] - epoch + 1) * epoch_cost
            eta_msg = "eta: {:s}".format(str(datetime.timedelta(seconds=int(eta_sec))))
            self.LOGGER.info(eta_msg)

            # 更新最佳权重
            if val_acc > best_acc:
                best_acc = val_acc
                best_weights = self.model.state_dict().copy()
                torch.save(best_weights, f"{model_dir}/best_model.pth")  # 最好权重
                self.LOGGER.info(f'Save Best model in path {model_dir}/best_model.pth')
        weight_path = f"{model_dir}/last_model.pth"
        torch.save(self.model.state_dict(), weight_path)  # 最后一轮权重
        self.LOGGER.info(f"Save Last model in path {weight_path}")
        self.LOGGER.info(f'Finish Training')
        self.LOGGER.info(f'best metric:{round(best_acc*100,2)}')