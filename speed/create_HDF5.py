import h5py
import cv2
import numpy as np
import os
from io import BytesIO
from PIL import Image
import json

def create_hdf5(background_path, hdf5_path, imgsz, max_images=350):
    # 获取图像文件列表
    files = [f for f in os.listdir(background_path) if f.lower().endswith(('.jpg', '.png', '.bmp'))]
    files = files[:min(len(files), max_images)]  # 限制最大图像数
    # 创建 HDF5 文件
    with h5py.File(hdf5_path, 'w') as f:
        # 创建数据集，形状为 (num_images, height, width, channels)
        dset = f.create_dataset('images', (len(files), imgsz[0], imgsz[1], 3), dtype='uint8')
        for i, file in enumerate(files):
            img = cv2.imread(os.path.join(background_path, file))
            if img is not None:
                img = cv2.resize(img, (imgsz[1], imgsz[0]))  # 调整为目标大小
                dset[i] = img
    print(f"HDF5 file created at {hdf5_path} with {len(files)} images.")


def create_hdf5_vlen_with_labels(instance_path, hdf5_path, label_dict, max_images=300): 
    """
    创建 HDF5 文件，存储序列化图像、类别和尺寸。
    label_dict: 字典，键为文件名，值为类别（如整数或字符串）。
    """

    AllFile, AllLen = {}, 0
    clsFolders = [it for it in os.listdir(instance_path) if os.path.isdir(f"{instance_path}/{it}")]
    print("clsFolders:",clsFolders)
    for clsFolder in clsFolders:
        FolderPath = os.path.join(instance_path,clsFolder)
        files = [f"{FolderPath}/{f}" for f in os.listdir(FolderPath) if f.lower().endswith(('.jpg', '.png', '.bmp'))]
        AllFile[clsFolder]=files
        AllLen += len(files)

    # 创建 HDF5 文件
    with h5py.File(hdf5_path, 'w') as f:
        # 变长字节类型存储图像
        dt = h5py.special_dtype(vlen=np.dtype('uint8'))
        img_dset = f.create_dataset('images', (AllLen,), dtype=dt)
        # 存储类别（假设为整数，若为字符串用 h5py.string_dtype()）
        label_dset = f.create_dataset('labels', (AllLen,), dtype='int32')
        # 存储尺寸
        size_dset = f.create_dataset('sizes', (AllLen, 2), dtype='int32')
        
        for FolderName, FolderLists in AllFile.items():
            for i, file in enumerate(FolderLists):
                img = cv2.imread(file)
                if img is not None:
                    # 转换为字节
                    img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
                    buffer = BytesIO()
                    img_pil.save(buffer, format="PNG")
                    img_dset[i] = np.frombuffer(buffer.getvalue(), dtype='uint8')
                    # 存储类别
                    label = label_dict.get(FolderName, 0)  # 默认类别为 0
                    # print("path:",file)
                    # print("label:",label)
                    label_dset[i] = label
                    # 存储尺寸
                    size_dset[i] = (img.shape[0], img.shape[1])
    
    print(f"HDF5 file created at {hdf5_path} with {AllLen} images and labels.")


if __name__ == "__main__":
    # # 示例用法
    # background_path = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/background"
    # hdf5_path = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/background.h5"
    # imgsz = (384, 4096)  # (height, width)
    # create_hdf5(background_path, hdf5_path, imgsz)


    instance_path = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai"
    hdf5_path = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/hdf5/haimiantai_vlen_with_labels.h5"
    label_json_path = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai/subfolder_to_id.json"
    with open(label_json_path, 'r') as file:
        label_dict = json.load(file)
    label_dict = {key:int(value) for key,value in label_dict.items()}
    create_hdf5_vlen_with_labels(instance_path, hdf5_path, label_dict)