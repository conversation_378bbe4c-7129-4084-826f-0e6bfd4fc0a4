import time
import numpy as np
from speed_test import generate_quadtree_positions

# for 循环实现
def for_loop_positions(bg_shape, small_shapes, num_small):
    positions = []
    max_attempts = num_small * 10
    for _ in range(num_small):
        attempts = 0
        while attempts < max_attempts:
            x = np.random.randint(0, bg_shape[1] - small_shapes[len(positions)][1])
            y = np.random.randint(0, bg_shape[0] - small_shapes[len(positions)][0])
            if all(not (x < p[0] + s[1] and x + small_shapes[len(positions)][1] > p[0] and
                        y < p[1] + s[0] and y + small_shapes[len(positions)][0] > p[1])
                   for p, s in zip(positions, small_shapes[:len(positions)])):
                positions.append((x, y))
                break
            attempts += 1
    return positions

# 四叉树实现（稍后提供修改版）
# 测试
bg_shape = (384, 4096)
num_small = 10
small_shapes = [(58, 106), (102, 72), (72, 94), (78, 88), (86, 68),
                (86, 68),   (92, 66), (100, 50), (98, 50), (58, 106)]
testTimes = 10

start = time.time()
for i in range(testTimes):
    positions_for = for_loop_positions(bg_shape, small_shapes, num_small)
print(f"for循环耗时: {time.time() - start:.4f} 秒")
# start = time.time()
# for i in range(10000):
#     positions_quad = generate_quadtree_positions(bg_shape, small_shapes, num_small)  # 修改版
# print(f"四叉树耗时: {time.time() - start:.4f} 秒")

print("for循环:",positions_for)
print("\n")
# print("四叉树:",positions_quad)