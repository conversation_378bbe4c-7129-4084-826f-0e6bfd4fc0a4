import numpy as np
import cv2
import torch
from torch.utils.data import Dataset, DataLoader
from concurrent.futures import ThreadPoolExecutor
import os

# def paste_instance(img_src, img_instance, mask_bg, box):
#     mask_fg = cv2.bitwise_not(mask_bg)
#     instance_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
#     img_src_roi_bg = cv2.bitwise_and(img_src[box[1]:box[3], box[0]:box[2], :], img_src[box[1]:box[3], box[0]:box[2], :],mask=mask_bg)
#     img_src[box[1]:box[3], box[0]:box[2], :] = cv2.bitwise_or(instance_fg, img_src_roi_bg)
#     return img_src
#
# def speed_sample_and_paste_instance(img_src, New_instance_gallery, hyp):
#     with ThreadPoolExecutor(max_workers=4) as executor:
#         futures = [executor.submit(paste_instance, img_src, img, mask_bg, box) for cls, img, mask_bg, box, org_P in New_instance_gallery]
#         for future in futures:
#             future.result()
#     return img_src

def paste_one(img_src, img_instance, mask_fg, mask_bg, box):
    x1, y1, x2, y2 = box
    roi_src = img_src[y1:y2, x1:x2]
    roi_bg = cv2.bitwise_and(roi_src, roi_src, mask=mask_bg)
    roi_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
    img_src[y1:y2, x1:x2] = cv2.bitwise_or(roi_fg, roi_bg)

# 多线程调用（确保线程安全）
def speed_sample_and_paste_instance(img_src, New_instance_gallery, hyp):
    with ThreadPoolExecutor() as executor:
        for cls, img_instance, mask_bg, box, org_P in New_instance_gallery:
            mask_fg = cv2.bitwise_not(mask_bg)
            executor.submit(paste_one, img_src, img_instance, mask_fg, mask_bg, box)
    return img_src



if __name__ == "__main__":

    background_path = r"E:\jiemi\Classification\retrieve\dataset\background\20250409194052212446_170.bmp"
    DATASET_DIR = r"D:\Desktop\Pinenut_2K_CLS_train"  # 数据集根目录
    imgsz = [192, 1024]

    from speed_test_mulproces import analyze_dataset
    instance_gallery = analyze_dataset(DATASET_DIR)
    instance_img_gallery = {}
    for clsName, imgLists in instance_gallery.items():  # [[path, [size[1],size[0]]]
        instance_img_gallery[clsName]={}
        for path, size in imgLists:
            img = cv2.imread(path)
            hsv_instance = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            lower_blue = np.array([100, 100, 100])
            upper_blue = np.array([140, 255, 255])
            mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
            instance_img_gallery[clsName][path]=[img, mask_bg, size]

    from speed_test_mulproces import optimized_epoch_batch
    epoch_batch = optimized_epoch_batch(instance_gallery, imgsz, num_batches=10000, num_processes=8)

    background_img = cv2.imread(background_path)
    background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))

    instance_gallery_temp = [['broken nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\broken nut\\train_20230315234315673_0_001_TH23038_0.png', [436, 35, 494, 141], 'M'],
                             ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230857379_0_002_TH22005_0.png', [4, 3, 80, 85], 'M'],
                             ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230858961_0_002_TH23061_0.png', [693, 68, 771, 152], 'M'],
                             ['Not a nut',  'D:\\Desktop\\Pinenut_2K_CLS_train\\Not a nut\\train_20230316020713238_0_003_TH22217_0.png', [162, 106, 282, 158], 'M'],
                             ['paint nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\paint nut\\train_20230315231939190_0_001_TH23179_0.png', [831, 92, 923, 158], 'M'],
                             ['paint nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\paint nut\\train_20230315231939303_0_003_TH22005_0.png', [324, 96, 408, 178], 'M'],
                             ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233025409_0_002_TH23019_0.png', [191, 35, 283, 87], 'M'],
                             ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233025307_0_002_TH22005_0.png', [781, 38, 847, 86], 'M']]

    from pytorch.engine.engine import ENGINE
    from pytorch.configs.config import update_config
    engine = ENGINE()
    __project__ = os.path.dirname(os.getcwd())
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_path}/Generation_1")

    import time
    cla_time = 0
    for i in range(10000):
        start = time.time()
        New_instance_gallery = [[cls, instance_img_gallery[cls][path][0], instance_img_gallery[cls][path][1], box, org_P] for cls, path, box, org_P in instance_gallery_temp]
        background_img = speed_sample_and_paste_instance(background_img, New_instance_gallery, engine.config)
        cla_time += (time.time() - start)
    print(f"paste_instance耗时: {cla_time:.4f} 秒")
    cv2.imwrite(r"D:\Desktop\background_img.jpg", background_img)

