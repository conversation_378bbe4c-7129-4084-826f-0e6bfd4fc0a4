import glob
import hashlib
import json
import os
import random
import shutil
import time
import sys
from itertools import repeat
from multiprocessing.pool import Pool, ThreadPool
from pathlib import Path
from threading import Thread
from zipfile import ZipFile
import cv2
import numpy as np
import torch
import torch.nn.functional as F
import yaml
from PIL import ExifTags, Image, ImageOps
from torch.utils.data import DataLoader, Dataset, dataloader, distributed
from tqdm import tqdm
import platform
from pytorch.data.preprocess.augmentations import Albumentations, augment_hsv, copy_paste, letterbox, mixup, random_perspective
from pytorch.utils.general import (LOGGER, check_dataset, check_requirements, check_yaml, clean_str, xyn2xy,
                           xywh2xyxy, xywhn2xyxy, xyxy2xywhn, torch_distributed_zero_first)

from pytorch.data.preprocess.instance_paste import sample_and_paste_instance
from speed_sample_and_paste_instance_mulproces_cache_img import speed_sample_and_paste_instance


def load_image_from_path(image_path):
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"Image not found: {image_path}")
    return img


class LoadImagesAndLabelsInstanceAUG(Dataset):
    def __init__(self, instance_path=None, class_names=None, batch_size=16, imgsz=[192,1024], augment=False,
                 hyp=None,  rect=False, image_weights=False, cache_images=False, single_cls=False, stride=32,
                 pad=0.0, prefix='', train_size=9000, background_folder=None):
        self.augment = augment
        self.imgsz = imgsz
        self.hyp = hyp
        self.epoch_batch = None
        self.image_weights = image_weights
        self.rect = False if image_weights else rect
        self.stride = stride
        self.albumentations = None
        self.train_size = train_size  # Training set size
        # Load background image
        self.background_files = [os.path.join(background_folder, f) for f in os.listdir(background_folder) if f.lower().endswith(('.jpg', '.png', '.bmp'))]
        self.background_img = []
        for i in range(min(len(self.background_files), 350)):
            self.background_img.append(cv2.resize(cv2.imread(self.background_files[i]), dsize=(self.imgsz[1], self.imgsz[0])))
        assert len(self.background_files) > 0, f"No background images found in {background_folder}"
        dataset_size = self.train_size
        self.labels = [np.empty((0, 5), dtype=np.float32) for _ in range(dataset_size)]

    def __len__(self):
        return self.train_size

    def __getitem__(self, index):
        hyp = self.hyp
        # Randomly select a background image
        img = self.background_img[random.choice(range(len(self.background_files)))]

        # ######背景HSV变换#######
        # if random.random() < 0.2 and hyp['Augment']['BG_HSV_Transform']:
        #     lower_thresh_bg = np.array(hyp['Augment']['bg_lower_hsv'])
        #     upper_thresh_bg = np.array(hyp['Augment']['bg_upper_hsv'])
        #     img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        #     mask = cv2.inRange(img_hsv, lower_thresh_bg, upper_thresh_bg)
        #     background_mask = cv2.bitwise_not(mask)  # 获取背景的掩膜
        #     hsv_bg = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
        #     hsv_bg_parameter = [0.05,0.3,0.2]
        #     hsv_bg_H, hsv_bg_S, hsv_bg_V = np.random.uniform(-1, 1, 3) * hsv_bg_parameter + 1
        #     hsv_bg[..., 0] = (hsv_bg[..., 0] * hsv_bg_H) % 180
        #     hsv_bg[..., 1] = np.clip(hsv_bg[..., 1] * hsv_bg_S, 0, 255)
        #     hsv_bg[..., 2] = np.clip(hsv_bg[..., 2] * hsv_bg_V, 0, 255)
        #     augmented_image = cv2.cvtColor(hsv_bg, cv2.COLOR_HSV2RGB)
        #     img[np.where(background_mask == 0)] = augmented_image[np.where(background_mask == 0)]

        # Initial labels are an empty array
        labels = np.empty((0, 5), dtype=np.float32)

        # Letterbox
        # shape = self.imgsz
        # h0, w0 = img.shape[:2]
        # background_img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
        # shapes = (h0, w0), ((background_img.shape[0] / h0, background_img.shape[1] / w0), pad)

        # init paste_instance
        instance_gallery = self.epoch_batch[index] if index<len(self.epoch_batch) else random.choice(self.epoch_batch)
        if random.random() < getattr(self, 'paste_instance_prob', 0):
            sample_scale_cur = [random.random() for _ in range(len(self.class_names))]
            img, labels = speed_sample_and_paste_instance(img, instance_gallery, hyp)

        # if hyp['Augment']['random_perspective']:
        #     img, labels = random_perspective(img, labels,
        #                                      degrees=hyp['Augment']['random_perspective_degrees'],
        #                                      translate=hyp['Augment']['random_perspective_translate'],
        #                                      scale=hyp['Augment']['random_perspective_scale'],
        #                                      shear=hyp['Augment']['random_perspective_shear'],
        #                                      perspective=hyp['Augment']['random_perspective_perspective'])

        nl = len(labels)  # number of labels
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1E-3)

        # if hyp['Augment']['albumentations']:
        #     # Albumentations
        #     img, labels = self.albumentations(img, labels)
        #     nl = len(labels)  # update after albumentations

        # if hyp['Augment']['augment_hsv']:
        #     # # HSV color-space
        #     augment_hsv(img, hgain=hyp['Augment']['augment_hsv_h'],
        #                      sgain=hyp['Augment']['augment_hsv_s'],
        #                      vgain=hyp['Augment']['augment_hsv_v'])

        if hyp['Augment']['flip']:
            # Flip up-down
            if random.random() < 0.3:
                img = np.flipud(img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]

            # Flip left-right
            if random.random() < 0.3:
                img = np.fliplr(img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # Convert
        img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        img = np.ascontiguousarray(img).astype(np.float32)
        return torch.from_numpy(img/255), labels_out  #, background_image_path, shapes

    @staticmethod
    def collate_fn(batch):
        img, label = zip(*batch)  # transposed
        for i, l in enumerate(label):
            l[:, 0] = i  # add target image index for build_targets()
        return torch.stack(img, 0), torch.cat(label, 0)



if __name__ == "__main__":
    DATASET_DIR = r"D:\Desktop\Pinenut_2K_CLS_train"  # 数据集根目录
    background_folder = r"E:\jiemi\Classification\retrieve\dataset\background"
    imgsz = [384, 2048]
    batch_size = 4

    from pytorch.engine.engine import ENGINE
    from pytorch.configs.config import update_config
    engine = ENGINE()
    __project__ = os.path.dirname(os.getcwd())
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_folder}/Generation_1")

    from speed_test_mulproces import analyze_dataset
    instance_gallery = analyze_dataset(DATASET_DIR)

    from speed_test_mulproces import optimized_epoch_batch
    epoch_batch = optimized_epoch_batch(instance_gallery, imgsz, num_batches=9000, num_processes=8)

    New_epoch_batch = []
    for batch in epoch_batch:
        for cls, path, box, org_P in batch:
            img = cv2.imread(path)
            hsv_instance = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            lower_blue = np.array([100, 100, 100])
            upper_blue = np.array([140, 255, 255])
            mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
            New_epoch_batch.append([cls, img, mask_bg, box, org_P])
    del hsv_instance, mask_bg, img


    dataset = LoadImagesAndLabelsInstanceAUG(None, None, batch_size, imgsz,
                                  augment=True,  # augmentation
                                  hyp=engine.config,  # hyperparameters
                                  train_size = 9000,
                                  background_folder = background_folder)


    dataset.epoch_batch = New_epoch_batch
    if sys.platform.startswith('linux'):
        nw = 8
    else:
        nw = 0
    dataloader = DataLoader(dataset,
                            batch_size=batch_size,
                            shuffle=True,
                            num_workers=0,  # 部署时改为nw
                            sampler=None,
                            pin_memory=True,
                            collate_fn=LoadImagesAndLabelsInstanceAUG.collate_fn)

    import time
    start = time.time()
    device = torch.device("cuda")
    for (inputs, labels) in tqdm(dataloader, total=len(dataloader)):
        inputs, labels = inputs.to(device, non_blocking=True), labels.to(device, non_blocking=True)
    print(f"dataloader耗时: {(time.time() - start):.4f} 秒")