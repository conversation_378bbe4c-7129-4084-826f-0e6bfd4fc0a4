import numpy as np
import cv2
import torch
from torch.utils.data import Dataset, DataLoader
from concurrent.futures import ThreadPoolExecutor
import os

def paste_instance(img_src, path, box):
    img_instance = cv2.imread(path)

    hsv_instance = cv2.cvtColor(img_instance, cv2.COLOR_BGR2HSV)
    # 定义要提取的蓝色范围 适用于1200机器下pinenut、almondshell，蓝色背景偏亮
    # lower_blue = np.array(hyp['Augment']['instance_lower_blue'])
    # upper_blue = np.array(hyp['Augment']['instance_upper_blue'])
    lower_blue = np.array([100,100,100])
    upper_blue = np.array([140,255,255])

    mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
    mask_fg = cv2.bitwise_not(mask_bg)

    # 图像分割，提取instance前景区域,以及img_src的背景区域，然后合并
    instance_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
    img_src_roi_bg = cv2.bitwise_and(img_src[box[1]:box[3], box[0]:box[2], :], img_src[box[1]:box[3], box[0]:box[2], :],
                                     mask=mask_bg)
    img_src[box[1]:box[3], box[0]:box[2], :] = cv2.bitwise_or(instance_fg, img_src_roi_bg)
    return img_src

def speed_sample_and_paste_instance(img_src, instance_gallery, hyp):
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(paste_instance, img_src, path, box) for cls, path, box, org_P in instance_gallery]
        for future in futures:
            future.result()
            # img_src = future.result()
    return img_src



if __name__ == "__main__":

    background_path = r"E:\jiemi\Classification\retrieve\dataset\background\20250409194052212446_170.bmp"
    imgsz = [192, 1024]
    background_img = cv2.imread(background_path)
    background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))
    instance_gallery = [['broken nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\broken nut\\train_20230315234315673_0_001_TH23038_0.png', [436, 35, 494, 141], 'M'], ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230857379_0_002_TH22005_0.png', [4, 3, 80, 85], 'M'], ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230858961_0_002_TH23061_0.png', [693, 68, 771, 152], 'M'], ['Not a nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\Not a nut\\train_20230316020713238_0_003_TH22217_0.png', [162, 106, 282, 158], 'M'], ['paint nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\paint nut\\train_20230315231939190_0_001_TH23179_0.png', [831, 92, 923, 158], 'M'], ['paint nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\paint nut\\train_20230315231939303_0_003_TH22005_0.png', [324, 96, 408, 178], 'M'], ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233025409_0_002_TH23019_0.png', [191, 35, 283, 87], 'M'], ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233025307_0_002_TH22005_0.png', [781, 38, 847, 86], 'M']]

    from pytorch.engine.engine import ENGINE
    from pytorch.configs.config import update_config
    engine = ENGINE()
    __project__ = os.path.dirname(os.getcwd())
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_path}/Generation_1")

    import time
    cla_time = 0
    for i in range(10000):
        start = time.time()
        background_img = cv2.imread(background_path)
        background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))
        background_img = speed_sample_and_paste_instance(background_img, instance_gallery, engine.config)
        cla_time += (time.time() - start)
    print(f"paste_instance耗时: {cla_time:.4f} 秒")
    cv2.imwrite(r"D:\Desktop\background_img.jpg", background_img)

