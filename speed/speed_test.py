import numpy as np
import os
from PIL import Image
import json
import concurrent.futures
from tqdm import tqdm
import random

####### 四叉树实现 #######
class QuadTree:
    def __init__(self, x, y, w, h):
        self.x, self.y, self.w, self.h = x, y, w, h
        self.positions = []  # 存储 (x, y, shape) 元组
        self.subdivided = False
        self.children = []

    def insert(self, pos, shape):
        """插入一个不重叠的位置，shape 为 (h, w)"""
        if not self.contains(pos, shape):
            return False
        if len(self.positions) < 4 and not self.subdivided:
            if all(not self.overlaps(pos, shape, p, s) for p, s in self.positions):
                self.positions.append((pos, shape))
                return True
        self.subdivide()
        for child in self.children:
            if child.insert(pos, shape):
                return True
        return False

    def contains(self, pos, shape):
        x, y = pos
        return (self.x <= x < self.x + self.w and
                self.y <= y < self.y + self.h and
                x + shape[1] <= self.x + self.w and
                y + shape[0] <= self.y + self.h)

    def overlaps(self, pos1, shape1, pos2, shape2):
        x1, y1 = pos1
        x2, y2 = pos2
        return not (x1 + shape1[1] <= x2 or x2 + shape2[1] <= x1 or
                    y1 + shape1[0] <= y2 or y2 + shape2[0] <= y1)

    def subdivide(self):
        if not self.subdivided:
            w, h = self.w // 2, self.h // 2
            self.children = [
                QuadTree(self.x, self.y, w, h),
                QuadTree(self.x + w, self.y, w, h),
                QuadTree(self.x, self.y + h, w, h),
                QuadTree(self.x + w, self.y + h, w, h)
            ]
            self.subdivided = True
def generate_quadtree_positions(bg_shape, small_shapes, num_small):
    """使用四叉树生成不重叠位置，small_shapes 为尺寸列表"""
    quad = QuadTree(0, 0, bg_shape[1], bg_shape[0])
    positions = []
    max_attempts = num_small * 10
    attempts = 0
    for i in range(num_small):
        shape = small_shapes[i % len(small_shapes)]  # 循环使用 small_shapes
        while attempts < max_attempts:
            x = np.random.randint(0, bg_shape[1] - shape[1])
            y = np.random.randint(0, bg_shape[0] - shape[0])
            if quad.insert((x, y), shape):
                positions.append((x, y))
                break
            attempts += 1
    return positions


# ####### for循环实现 #######
def for_loop_positions(bg_shape, batch_shapes, org_position, num_small):
    positions = []
    max_attempts = num_small * 10
    for _ in range(num_small):
        attempts = 0
        while attempts < max_attempts:
            if org_position[len(positions)]== "M":
                x = np.random.randint(0, bg_shape[1] - batch_shapes[len(positions)][1])
                y = np.random.randint(0, bg_shape[0] - batch_shapes[len(positions)][0])
            else:
                if org_position == "T" or org_position == "B":
                    x = np.random.randint(0, bg_shape[1] - batch_shapes[len(positions)][1])
                    y = 0 if org_position == "T" else (bg_shape[0]-batch_shapes[len(positions)][0])
                else:
                    x = 0 if org_position == "L" else (bg_shape[1]-batch_shapes[len(positions)][1])
                    y = np.random.randint(0, bg_shape[0] - batch_shapes[len(positions)][0])
            if all(not (x < p[0] + s[1] and x + batch_shapes[len(positions)][1] > p[0] and
                        y < p[1] + s[0] and y + batch_shapes[len(positions)][0] > p[1])
                   for p, s in zip(positions, batch_shapes[:len(positions)])):
                positions.append((x, y))
                break
            attempts += 1
        else:  # 当while循环因attempts>=max_attempts退出时执行
            positions.append(None)  # 只要有一个位置找不到就返回None
            return positions
    return positions

# ####### 数据处理，instance_gallery构建 #######
def analyze_dataset(dataset_dir):
    statistics = {}
    category_dirs = [os.path.join(dataset_dir, d)
                     for d in os.listdir(dataset_dir)
                     if os.path.isdir(os.path.join(dataset_dir, d))]

    def process_category(category_dir):
        """处理单个类别目录，返回该类别下所有图像的尺寸信息"""
        category_name = os.path.basename(category_dir)
        image_files = []
        # 支持的图像格式
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        # 收集所有图像文件
        for root, _, files in os.walk(category_dir):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in valid_extensions:
                    image_files.append(os.path.join(root, file))

        def get_image_size(image_path):
            """获取单个图像的尺寸"""
            try:
                with Image.open(image_path) as img:
                    return image_path, img.size
            except Exception as e:
                print(f"Error reading {image_path}: {e}")
                return image_path, None

        # 并行处理图像获取尺寸
        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(tqdm(executor.map(get_image_size, image_files),
                                total=len(image_files),
                                desc=f"Processing {category_name}"))
        valid_results = [[path, [size[1],size[0]]] for path, size in results if size is not None]
        return category_name, valid_results

    # 处理每个类别
    for category_dir in tqdm(category_dirs, desc="Processing categories"):
        category_name, image_info = process_category(category_dir)
        statistics[category_name] = image_info
    return statistics


if __name__ == "__main__":

    DATASET_DIR = r"D:\Desktop\Pinenut_2K_CLS_train"  # 数据集根目录
    instance_gallery = analyze_dataset(DATASET_DIR)

    import time
    start = time.time()
    epoch_batch = []
    for i in range(10000):

        bg_shape = [192,1024]
        sample_scale_cur = [random.random() for _ in range(len(instance_gallery))]
        class_ratio = [int(round(it * 3)) for it in sample_scale_cur]  # 实际粘贴图片的数量
        batch_path = []
        batch_cls = []
        batch_shapes = []
        org_position = []
        num_small = 0
        # for sample_num, (cls, img_lists) in zip(class_ratio, instance_gallery.items()):
        #     instance_files = random.choices(img_lists, k=int(sample_num))
        #     batch_path += [instance_file[0] for instance_file in instance_files]
        #     batch_shapes += [instance_file[1] for instance_file in instance_files]
        #     batch_cls += [cls for instance_file in instance_files]
        #     org_position += [os.path.basename(instance_file[0]).split(".")[0].split("_")[-1] if os.path.basename(instance_file[0]).split(".")[0].split("_")[-1] in ["L", "R", "T", "B"] else "M" for instance_file in instance_files]
        #     num_small += sample_num
        instance_files = [[instance_file[0],instance_file[1],cls] for sample_num, (cls, img_lists) in zip(class_ratio, instance_gallery.items())
                             for instance_file in random.choices(img_lists, k=int(sample_num))]
        batch_path = [instance_file[0] for instance_file in instance_files]
        batch_shapes = [instance_file[1] for instance_file in instance_files]
        batch_cls = [instance_file[2] for instance_file in instance_files]
        org_position = [os.path.basename(instance_file[0]).split(".")[0].split("_")[-1] if
                        os.path.basename(instance_file[0]).split(".")[0].split("_")[-1] in ["L", "R", "T","B"] else "M" for
                        instance_file in instance_files]
        num_small = len(instance_files)

        positions = for_loop_positions(bg_shape, batch_shapes, org_position, num_small)  # 运用for循环实现
        if positions == None or (None in positions):
            continue
        batch = [[cls,path,[pos[0], pos[1], imgsize[1], imgsize[0]],orP] for cls, pos, imgsize, path, orP in zip(batch_cls,positions, batch_shapes, batch_path, org_position)]
        epoch_batch.append(batch)
    print(f"for循环耗时: {time.time() - start:.4f} 秒")
