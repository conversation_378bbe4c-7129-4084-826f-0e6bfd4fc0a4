import numpy as np
import cv2
import torch
from torch.utils.data import Dataset, DataLoader
from concurrent.futures import ThreadPoolExecutor
import os
import random
from pytorch.utils.general import bbox_ioa

def paste_instance(img_src, labels_src, img_instance, label_instance, org_position="M", hyp=None):
    img_src_w = img_src.shape[1]
    img_src_h = img_src.shape[0]
    img_instance_w = img_instance.shape[1]
    img_instance_h = img_instance.shape[0]

    # 如果实例图像的高度或宽度大于源图像，则需要缩小实例图像的尺寸
    if img_instance_h > img_src_h or img_instance_w > img_src_w:
        resize_ratio = max(img_instance.shape[0]/img_src.shape[0], img_instance.shape[1]/img_src.shape[1])
        width = int(img_instance.shape[1] / resize_ratio)
        height = int(img_instance.shape[0] / resize_ratio)
        dsize = (width, height)
        img_instance = cv2.resize(img_instance,  dsize=dsize)
        img_instance_w = img_instance.shape[1]
        img_instance_h = img_instance.shape[0]

    box = np.zeros(4)
    if org_position != "M":
        # X0orig, Y0orig, X1orig, Y1orig = org_position
        # 寻找合适的粘贴区域
        if org_position == "T" or org_position == "B":
            for i in range(10):
                cx_min = min(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
                cx_max = max(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
                cx = random.randint(cx_min, cx_max)
                xmin = cx - int(img_instance_w / 2)
                xmax = xmin + img_instance_w
                if img_instance_w == img_src_w:
                    xmin = 0
                    xmax = img_src_w
                if org_position == "T":
                    box_cur = np.array([xmin, 0, xmax, img_instance_h])
                else:
                    box_cur = np.array([xmin, int(img_src_h-img_instance_h), xmax, img_src_h])
                ioa = bbox_ioa(box_cur, labels_src[:, 1:5])  # 计算与源标签的交并比
                if (ioa < 0.10).all():  # 允许最多10%的重叠
                    box = box_cur
                    break
                else:
                    continue
        elif org_position == "L" or org_position == "R":
            for i in range(10):
                cy_min = min(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
                cy_max = max(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
                cy = random.randint(cy_min, cy_max)
                ymin = cy - int(img_instance_h / 2)
                ymax = ymin + img_instance_h
                if img_instance_h == img_src_h:
                    ymin = 0
                    ymax = img_src_h
                if org_position == "L":
                    box_cur = np.array([0, ymin, img_instance_w, ymax])
                else:
                    box_cur = np.array([img_src_w - img_instance_w, ymin, img_src_w, ymax])
                ioa = bbox_ioa(box_cur, labels_src[:, 1:5])  # 计算与源标签的交并比
                if (ioa < hyp['Augment']['ioa']).all():  # 允许最多hyp['Augment']['ioa']的重叠
                    box = box_cur
                    break
                else:
                    continue
    else:
        for i in range(10):
            # 随机选取粘贴的中心点，并计算粘贴区域矩形框坐标
            cx_min = min(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
            cx_max = max(int(img_instance_w / 2) + 1, img_src_w - int(img_instance_w / 2) - 1)
            cy_min = min(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
            cy_max = max(int(img_instance_h / 2) + 1, img_src_h - int(img_instance_h / 2) - 1)
            cx = random.randint(cx_min, cx_max)
            cy = random.randint(cy_min, cy_max)
            xmin = cx - int(img_instance_w / 2)
            ymin = cy - int(img_instance_h / 2)
            xmax = xmin + img_instance_w
            ymax = ymin + img_instance_h

            if img_instance_w == img_src_w:
                xmin = 0
                xmax = img_src_w

            if img_instance_h == img_src_h:
                ymin = 0
                ymax = img_src_h

            box_cur = np.array([xmin, ymin, xmax, ymax])

            ioa = bbox_ioa(box_cur, labels_src[:, 1:5])   # 计算与源标签的交并比
            if (ioa < hyp['Augment']['ioa']).all():  # 允许最多hyp['Augment']['ioa']的重叠
                box = box_cur
                break
            else:
                continue

    if np.all(box==0): #10次都没寻找到paste区域则直接返回原图
        return img_src, labels_src
    else:
        hsv_instance = cv2.cvtColor(img_instance, cv2.COLOR_BGR2HSV)
        # 定义要提取的蓝色范围 适用于1200机器下pinenut、almondshell，蓝色背景偏亮
        lower_blue = np.array(hyp['Augment']['instance_lower_blue'])
        upper_blue = np.array(hyp['Augment']['instance_upper_blue'])
        # print("lower_blue:",lower_blue)
        # print("upper_blue:",upper_blue)

        mask_bg = cv2.inRange(hsv_instance, lower_blue, upper_blue)
        mask_fg = cv2.bitwise_not(mask_bg)

        # 图像分割，提取instance前景区域,以及img_src的背景区域，然后合并
        instance_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
        img_src_roi_bg = cv2.bitwise_and(img_src[box[1]:box[3], box[0]:box[2], :], img_src[box[1]:box[3], box[0]:box[2], :], mask=mask_bg)
        img_src[box[1]:box[3], box[0]:box[2], :] = cv2.bitwise_or(instance_fg, img_src_roi_bg)
        # 拼接label
        labels_src = np.concatenate((labels_src, [[label_instance, *box]]), 0)
        return img_src, labels_src


def  sample_and_paste_instance(background_img, instance_gallery, hyp):
    labels_src =  np.empty((0, 5), dtype=np.float32)
    for instance_file in instance_gallery:
        img_instance = cv2.imread(instance_file[1])
        org_position = os.path.basename(instance_file[1]).split(".")[0].split("_")[-1]
        org_position = org_position if org_position in ["L", "R", "T", "B"] else "M"
        background_img, labels_src = paste_instance(background_img, labels_src, img_instance, 0, org_position, hyp)
    return background_img



if __name__ == "__main__":

    background_path = r"E:\jiemi\Classification\retrieve\dataset\background\20250409194052212446_170.bmp"
    imgsz = [192, 1024]
    # background_img = cv2.imread(background_path)
    # background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))
    instance_gallery = [['broken nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\broken nut\\train_20230315234315673_0_001_TH23038_0.png', [871, 48, 58, 106], 'M'], ['broken nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\broken nut\\train_20230315234315764_0_000_TH23179_0.png', [59, 57, 102, 72], 'M'], ['broken nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\broken nut\\train_20230315234316450_0_000_TH22005_0.png', [600, 94, 84, 92], 'M'], ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230858961_0_003_TH23019_0.png', [178, 17, 78, 88], 'M'], ['normal nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\normal nut\\train_20230315230858097_0_002_TH23038_0.png', [332, 81, 72, 94], 'M'], ['Not a nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\Not a nut\\train_20230316020713227_0_003_TH23038_0.png', [477, 41, 88, 52], 'M'], ['paint nut', 'D:\\Desktop\\Pinenut_2K_CLS_train\\paint nut\\train_20230315231939336_0_000_TH23019_0.png', [624, 14, 76, 60], 'M'], ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233024805_0_001_TH23179_0.png', [14, 136, 100, 50], 'M'], ['shell', 'D:\\Desktop\\Pinenut_2K_CLS_train\\shell\\train_20230315233020868_0_000_TH22217_0.png', [737, 0, 90, 34], 'M']]

    from pytorch.engine.engine import ENGINE
    from pytorch.configs.config import update_config
    engine = ENGINE()
    __project__ = os.path.dirname(os.getcwd())
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_path}/Generation_1")

    import time
    cla_time = 0
    for i in range(10000):
        start = time.time()
        background_img = cv2.imread(background_path)
        background_img = cv2.resize(background_img, dsize=(imgsz[1], imgsz[0]))
        background_img = sample_and_paste_instance(background_img, instance_gallery, engine.config)
        cla_time += (time.time() - start)
    print(f"paste_instance耗时: {cla_time:.4f} 秒")

    cv2.imwrite(r"D:\Desktop\background_img.jpg", background_img)

