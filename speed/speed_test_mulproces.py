import numpy as np
import os
from PIL import Image
import json
from tqdm import tqdm
import random
import cv2
import torch
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures
from multiprocessing import Pool
from functools import partial


def analyze_dataset(dataset_dir):
    statistics = {}
    category_dirs = [os.path.join(dataset_dir, d) for d in os.listdir(dataset_dir) if os.path.isdir(os.path.join(dataset_dir, d))]

    def process_category(category_dir):
        """处理单个类别目录，返回该类别下所有图像的尺寸信息"""
        category_name = os.path.basename(category_dir)
        image_files = []
        # 支持的图像格式
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        # 收集所有图像文件
        for root, _, files in os.walk(category_dir):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in valid_extensions:
                    image_files.append(os.path.join(root, file))

        def get_image_size(image_path):
            """获取单个图像的尺寸"""
            try:
                with Image.open(image_path) as img:
                    return image_path, img.size  # size = [W, H]
            except Exception as e:
                print(f"Error reading {image_path}: {e}")
                return image_path, None

        # 并行处理图像获取尺寸
        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(tqdm(executor.map(get_image_size, image_files),
                                total=len(image_files),
                                desc=f"Processing {category_name}"))
        # [size[1],size[0]] = [H,W]
        valid_results = [[path, [size[1],size[0]]] for path, size in results if size is not None]
        return category_name, valid_results

    # 处理每个类别
    for category_dir in tqdm(category_dirs, desc="Processing categories"):
        category_name, image_info = process_category(category_dir)
        statistics[category_name] = image_info
    return statistics


def analyze_dataset_txt(txt_path):
    statistics = {}

    # 读取txt文件
    def read_txt_file(txt_path):
        image_info = {}
        with open(txt_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    image_path, category = parts[0], parts[1]
                    if category not in image_info:
                        image_info[category] = []
                    image_info[category].append(image_path)
        return image_info

    def get_image_size(image_path):
        """获取单个图像的尺寸"""
        try:
            with Image.open(image_path) as img:
                return image_path, img.size  # size = [W, H]
        except Exception as e:
            print(f"Error reading {image_path}: {e}")
            return image_path, None

    def process_category(category_name, image_files):
        """处理单个类别下的图像列表"""
        # 并行处理图像获取尺寸
        with concurrent.futures.ThreadPoolExecutor() as executor:
            results = list(tqdm(executor.map(get_image_size, image_files),
                                total=len(image_files),
                                desc=f"Processing {category_name}"))
        # [size[1],size[0]] = [H,W]
        valid_results = [[path, [size[1], size[0]]] for path, size in results if size is not None]
        return category_name, valid_results

    # 读取txt并处理
    image_info = read_txt_file(txt_path)

    # 处理每个类别
    for category_name, image_files in tqdm(image_info.items(), desc="Processing categories"):
        category_name, image_info_list = process_category(category_name, image_files)
        statistics[category_name] = image_info_list

    return statistics


def bbox_ioa(box1, box2, eps=1E-7):
    box2 = box2.transpose()
    b1_x1, b1_y1, b1_x2, b1_y2 = box1[0], box1[1], box1[2], box1[3]
    b2_x1, b2_y1, b2_x2, b2_y2 = box2[0], box2[1], box2[2], box2[3]
    inter_area = (np.minimum(b1_x2, b2_x2) - np.maximum(b1_x1, b2_x1)).clip(0) * \
                 (np.minimum(b1_y2, b2_y2) - np.maximum(b1_y1, b2_y1)).clip(0)
    box2_area = (b2_x2 - b2_x1) * (b2_y2 - b2_y1) + eps
    return inter_area / box2_area


# ####### for循环实现 #######
def for_loop_positions(bg_shape, batch_shapes, org_position, num_small):
    positions = []
    ALLbox = np.empty((0, 4), dtype=np.float32)
    max_attempts = num_small * 10
    for _ in range(num_small):
        attempts = 0
        while attempts < max_attempts:
            if org_position[len(positions)]== "M":  # bg_shape:[H,W] ,  batch_shapes: [H,W]
                x = np.random.randint(0, bg_shape[1] - batch_shapes[len(positions)][1])
                y = np.random.randint(0, bg_shape[0] - batch_shapes[len(positions)][0])
            else:
                if org_position == "T" or org_position == "B":
                    x = np.random.randint(0, bg_shape[1] - batch_shapes[len(positions)][1])
                    y = 0 if org_position == "T" else (bg_shape[0]-batch_shapes[len(positions)][0])
                else:
                    x = 0 if org_position == "L" else (bg_shape[1]-batch_shapes[len(positions)][1])
                    y = np.random.randint(0, bg_shape[0] - batch_shapes[len(positions)][0])
            # batch_shapes: [H, W]  ,  pos : [x, y]
            box_cur = np.array([x, y, x+batch_shapes[len(positions)][1], y+batch_shapes[len(positions)][0]])
            ioa = bbox_ioa(box_cur, ALLbox)  
            if (ioa < 0.1).all():  
                ALLbox = np.concatenate((ALLbox, [[*box_cur]]), 0)
                positions.append([x, y])
                break
            attempts += 1
        else:
            positions.append([])   # 当while循环因attempts>=max_attempts退出时执行
    return positions


def generate_batch_data(batch_idx, instance_gallery, bg_shape):
    sample_scale_cur = [random.random() for _ in range(len(instance_gallery))]
    class_ratio = [int(round(it * 3)) for it in sample_scale_cur]  # 实际粘贴图片的数量
    batch_path = []
    batch_cls = []
    batch_shapes = []
    org_position = []
    num_small = 0
    temp_instance_files = [[id, size, cls, pos] for sample_num, (cls, img_lists) in zip(class_ratio, instance_gallery.items())
                         for id, size, pos in random.choices(img_lists, k=int(sample_num))]
    batch_index = [id for id, size, cls, pos in temp_instance_files]
    batch_size = [size for id, size, cls, pos in temp_instance_files]
    batch_cls = [cls for id, size, cls, pos in temp_instance_files]
    org_position = [pos for id, size, cls, pos in temp_instance_files]

    num_small = len(temp_instance_files)
    positions = for_loop_positions(bg_shape, batch_size, org_position, num_small)  # 运用for循环实现
    batch = []        
    for cls, pos, imgsize, index, orP in zip(batch_cls, positions, batch_size, batch_index, org_position):
        if pos == []:
            continue
        batch.append([cls, index, [pos[0], pos[1], pos[0]+imgsize[1], pos[1]+imgsize[0]], orP])
    return batch

def optimized_epoch_batch(instance_gallery, bg_shape, num_batches=10000, num_processes=4):
    with Pool(num_processes) as pool:
        epoch_batch = pool.map(partial(generate_batch_data, instance_gallery=instance_gallery, bg_shape=bg_shape), range(num_batches))
    return epoch_batch

# def optimized_epoch_batch(instance_gallery, bg_shape, num_batches=9000, num_threads=4):
#     try:
#         with ThreadPoolExecutor(max_workers=num_threads) as executor:
#             epoch_batch = list(executor.map(partial(generate_batch_data, instance_gallery=instance_gallery, bg_shape=bg_shape), range(num_batches)))
#         return epoch_batch
#     except Exception as e:
#         print(f"ThreadPool error: {e}")
#         raise
#     pass


################### for循环实例粘贴 ######################
def speed_sample_and_paste_instance(img_src, New_instance_gallery, hyp):
    for cls, img_instance, mask_bg, box, org_P in New_instance_gallery:
        mask_fg = cv2.bitwise_not(mask_bg)
        x1, y1, x2, y2 = box[0], box[1], box[2], box[3]
        roi_src = img_src[y1:y2, x1:x2]
        roi_bg = cv2.bitwise_and(roi_src, roi_src, mask=mask_bg)
        roi_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
        img_src[y1:y2, x1:x2] = cv2.bitwise_or(roi_fg, roi_bg)
    return img_src
###########################################################


# #################### 多线程实例粘贴 ###################### 
# def paste_instance(img_src, img_instance, mask_fg, mask_bg, box):
#     x1, y1, x2, y2 = box
#     roi_src = img_src[y1:y2, x1:x2]
#     roi_bg = cv2.bitwise_and(roi_src, roi_src, mask=mask_bg)
#     roi_fg = cv2.bitwise_and(img_instance, img_instance, mask=mask_fg)
#     img_src[y1:y2, x1:x2] = cv2.bitwise_or(roi_fg, roi_bg)


# def speed_sample_and_paste_instance(img_src, New_instance_gallery, hyp):
#     with ThreadPoolExecutor() as executor:
#         for cls, img_instance, mask_bg, box, org_P in New_instance_gallery:
#             mask_fg = cv2.bitwise_not(mask_bg)
#             executor.submit(paste_instance, img_src, img_instance, mask_fg, mask_bg, box)
#     return img_src
# #############################################################