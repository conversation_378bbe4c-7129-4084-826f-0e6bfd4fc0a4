import torch
import torch.nn as nn
import numpy as np
import cv2
import glob
import os
import json, yaml
from tqdm import tqdm
from pathlib import Path
from pathlib import Path
from qingwei.ts_utils.datasets import create_dataloader
from qingwei.ts_utils.general import (
    coco80_to_coco91_class, check_dataset, check_img_size, non_max_suppression, scale_coords,
    xyxy2xywh, clip_coords, xywh2xyxy, box_iou, set_logging)
from qingwei.ts_utils.metrics import ap_per_class
from qingwei.ts_utils.torch_utils import select_device, time_synchronized
from onnx_quantize_tool.common.register import onnx_infer_func, pytorch_model



# 根据物料修改如下参数
config_yolov5s={
'nc': 11,  # number of classes
'imgsz' :[384, 4096], #  h,w
'iou':0.45,
'conf':0.25,
'dataPath':'/project/qingwei/data_config.yaml',# docker
# 根据使用的模型自动选择对应的anchors值 - YOLOv5N
'anchors':[
  # 3 anchors for YOLOv5N
  [10, 13, 16, 30, 33, 23],  # P3/8
  [30, 61, 62, 45, 59, 119],  # P4/16
  [116, 90, 156, 198, 373, 326]  # P5/32
]
}

class DetectWithSigmoid(torch.nn.Module):
    def __init__(self):
        super(DetectWithSigmoid, self).__init__()

    def forward(self, x):
        for i in range(self.nl):
            x[i] = self.m[i](x[i])
            x[i] = x[i].sigmoid()

        return x


def letterbox_my(im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
    # Resize and pad image while meeting stride-multiple constraints
    shape = im.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better val mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
    return im, ratio, (dw, dh)

class Detect_process(nn.Module):

    def __init__(self, anchors, nc=1, nl=3, ch=3, na=3, inplace=True):
        self.nc = nc  # number of classes
        self.no = nc + 5  # number of outputs per anchor
        self.nl = len(anchors)  # number of detection layers
        self.na = len(anchors[0]) // 2  # number of anchors
        self.grid = [torch.zeros(1)] * self.nl  # init grid
        self.anchor_grid = [torch.zeros(1)] * self.nl  # init anchor grid
        self.anchors = torch.tensor(anchors).float().view(self.nl, -1, 2)  # shape(nl,na,2)

        self.inplace = inplace  # use in-place ops (e.g. slice assignment)
        if self.nl == 3:
            self.stride = torch.Tensor([8, 16, 32])  # 3 anchors
        elif self.nl == 2:
            if (anchors[0] == [5,6, 8,14] and anchors[1] == [10,13, 16,30]):
                self.stride = torch.Tensor([4, 8])  
            else:
                self.stride = torch.Tensor([8, 16])  # 2 anchors
        elif self.nl == 1:
            self.stride = torch.Tensor([16])
        self.anchors /= self.stride.view(-1, 1, 1)

    def post_process(self, x):
        # print('anchor:', self.anchors)
        # print(f"onnx : ")
        z = []  # inference output
        for i in range(len(x)):
            # print(f"in x[{i}] :{x[i].cpu().numpy().flatten()[:10]} ")
            # x[i] = torch.nn.functional.sigmoid(x[i])
            bs, _, ny, nx = x[i].shape  # x(bs,255,20,20) to x(bs,3,20,20,85)
            x[i] = x[i].view(bs, self.na, self.no, ny, nx).permute(0, 1, 3, 4, 2).contiguous()
            if self.grid[i].shape[2:4] != x[i].shape[2:4]:
                self.grid[i], self.anchor_grid[i] = self._make_grid(nx, ny, i)
            y = x[i]
            # y = x[i].sigmoid()
            if self.inplace:
                y[..., 0:2] = (y[..., 0:2] * 2. - 0.5 + self.grid[i]) * self.stride[i]  # xy # ycx
                y[..., 2:4] = (y[..., 2:4] * 2) ** 2 * self.anchor_grid[i]  # wh
            else:  # for YOLOv5 on AWS Inferentia https://github.com/ultralytics/yolov5/pull/2953
                xy = (y[..., 0:2] * 2 - 0.5 + self.grid[i]) * self.stride[i]  # xy
                wh = (y[..., 2:4] * 2) ** 2 * self.anchor_grid[i]  # wh
                y = torch.cat((xy, wh, y[..., 4:]), -1)
            z.append(y.view(bs, -1, self.no))
        return torch.cat(z, 1)


    def _make_grid(self, nx=20, ny=20, i=0): #pytorch
        d = self.anchors[i].device
        # print('anchor:', self.anchors[i])
        # 1.13.1+cu117
        # if check_version(torch.__version__, '1.10.0'):  # torch>=1.10.0 meshgrid workaround for torch>=0.7 compatibility
        yv, xv = torch.meshgrid([torch.arange(ny).to(d), torch.arange(nx).to(d)], indexing='ij')
        # else:
        #     yv, xv = torch.meshgrid([torch.arange(ny).to(d), torch.arange(nx).to(d)])
        grid = torch.stack((xv, yv), 2).expand((1, self.na, ny, nx, 2)).float()
        anchor_grid = (self.anchors[i].clone() * self.stride[i]) \
            .view((1, self.na, 1, 1, 2)).expand((1, self.na, ny, nx, 2)).float()
        return grid, anchor_grid


class ArgParser:
    def __init__(self, args: dict):
        for k, v in args.items():
            setattr(self, k, v)

def affine_box(dets, ratio, dw, dh):
    for box in dets:
        box[0]= int((box[0]-dw)/ratio[0])
        box[1]= int((box[1]-dh)/ratio[1])
        box[2]= int((box[2]-dw)/ratio[0])
        box[3]= int((box[3]-dh)/ratio[1])
    return dets
@ onnx_infer_func.register("infer_yolov5_v6_0")
def infer_yolov5_v6_0(executor):
    args_dict = dict(
        data=config_yolov5s['dataPath'],
        single_cls=False,
    )   
    iteration = executor.iteration
    batch_size = executor.batch_size
    device='cpu'
    task='val'
    augment=False
    merge=False
    verbose=False
    save_txt=False
    imgsz = (config_yolov5s['imgsz'][0], config_yolov5s['imgsz'][1])
    training = False
    device = 'cpu'
    opt = ArgParser(args_dict)
    data = opt.data
    if data=='data/coco.yaml':
        save_json=True
    else:
        save_json=False
    set_logging()

    device = select_device(device, batch_size=1)
    # imgsz = check_img_size(imgsz, s=32)  # check img_size
    detect_process = Detect_process(config_yolov5s['anchors'], nc=config_yolov5s['nc'])

    with open(data) as f:
        data = yaml.load(f, Loader=yaml.FullLoader)  # model dict
    check_dataset(data)  # check
    nc = 1 if opt.single_cls else int(data['nc'])  # number of classes
    iouv = torch.linspace(0.5, 0.95, 10).to(device)  # iou vector for mAP@0.5:0.95
    niou = iouv.numel()
    # Dataloader
    if not training:
        path = data['val']  # path to val/test images
        dataloader = create_dataloader(path, imgsz, 1, 32, opt,
                                       hyp=None, augment=False, cache=False, pad=0.5, rect=True)[0]
    seen = 0
    coco91class = coco80_to_coco91_class()
    s = ('%20s' + '%12s' * 6) % ('Class', 'Images', 'Targets', 'P', 'R', 'mAP@.5', 'mAP@.5:.95')
    p, r, f1, mp, mr, map50, map, t0, t1 = 0., 0., 0., 0., 0., 0., 0., 0., 0.
    jdict, stats, ap, ap_class = [], [], [], []
    for batch_i, (img, targets, paths, shapes) in enumerate(tqdm(dataloader, desc=s), 1):

        im0 = img
        nb, _, height, width = im0.shape  # batch size, channels, height, width
        whwh = torch.Tensor([width, height, width, height])
        # img, ratio, (dw,dh) = letterbox_my(img.squeeze(0).permute(1,2,0).numpy(), (48, 1024), auto=False, stride=32)
        # img = torch.from_numpy(img.transpose(2,0,1))

        img = img.float()  # uint8 to fp16/32
        img /= 255.0  # 0 - 255 to 0.0 - 1.0

        if img.ndimension() == 3:
            img = img.unsqueeze(0)
        # Inference
        t1 = time_synchronized()

        pred = executor.forward(img.numpy())
        num_heads = len(config_yolov5s['anchors'])  
        pred = [torch.from_numpy(pred[i]) for i in range(num_heads)]  # 动态生成 pred 列表
        pred = detect_process.post_process(pred)


        # Apply NMS
        output = non_max_suppression(pred, config_yolov5s['conf'], config_yolov5s['iou'])
        # print(output[0])
        # print(output[0].shape)

        t2 = time_synchronized()
        # Statistics per image
        for si, pred in enumerate(output):
            labels = targets[targets[:, 0] == si, 1:]
            # print(labels)
            nl = len(labels)
            tcls = labels[:, 0].tolist() if nl else []  # target class
            seen += 1

            if pred is None:
                if nl:
                    stats.append((torch.zeros(0, niou, dtype=torch.bool), torch.Tensor(), torch.Tensor(), tcls))
                continue

            # Clip boxes to image bounds
            # pred = affine_box(pred, ratio, dw, dh)
            clip_coords(pred, (height, width))

            # Append to pycocotools JSON dictionary
            if save_json:
                # [{"image_id": 42, "category_id": 18, "bbox": [258.15, 41.29, 348.26, 243.78], "score": 0.236}, ...
                image_id = Path(paths[si]).stem
                box = pred[:, :4].clone()  # xyxy
                scale_coords(img[si].shape[1:], box, shapes[si][0], shapes[si][1])  # to original shape
                box = xyxy2xywh(box)  # xywh
                box[:, :2] -= box[:, 2:] / 2  # xy center to top-left corner
                for p, b in zip(pred.tolist(), box.tolist()):
                    jdict.append({'image_id': int(image_id) if image_id.isnumeric() else image_id,
                                  'category_id': coco91class[int(p[5])],
                                  'bbox': [round(x, 3) for x in b],
                                  'score': round(p[4], 5)})

            # Assign all predictions as incorrect
            correct = torch.zeros(pred.shape[0], niou, dtype=torch.bool)
            if nl:
                detected = []  # target indices
                tcls_tensor = labels[:, 0]

                # target boxes
                tbox = xywh2xyxy(labels[:, 1:5]) * whwh


                # Per target class
                for cls in torch.unique(tcls_tensor):
                    ti = (cls == tcls_tensor).nonzero(as_tuple=False).view(-1)  # prediction indices
                    pi = (cls == pred[:, 5]).nonzero(as_tuple=False).view(-1)  # target indices

                    # Search for detections
                    if pi.shape[0]:
                        # Prediction to target ious
                        ious, i = box_iou(pred[pi, :4], tbox[ti]).max(1)  # best ious, indices

                        # Append detections
                        for j in (ious > iouv[0]).nonzero(as_tuple=False):
                            d = ti[i[j]]  # detected target
                            if d not in detected:
                                detected.append(d)
                                correct[pi[j]] = ious[j] > iouv  # iou_thres is 1xn
                                if len(detected) == nl:  # all targets already located in image
                                    break
            # Append statistics (correct, conf, pcls, tcls)
            stats.append((correct.cpu(), pred[:, 4].cpu(), pred[:, 5].cpu(), tcls))
        if batch_i >= iteration:
            break

    # import pdb;pdb.set_trace()
    # Compute statistics
    stats = [np.concatenate(x, 0) for x in zip(*stats)]  # to numpy
    # print(stats)
    if len(stats) and stats[0].any():
        tp, fp, p, r, f1, ap, ap_class = ap_per_class(*stats)
        # import pdb;pdb.set_trace()
        ap50, ap = ap[:, 0], ap.mean(1)  # AP@0.5, AP@0.5:0.95
        mp, mr, map50, map = p.mean(), r.mean(), ap50.mean(), ap.mean()
        nt = np.bincount(stats[3].astype(np.int64), minlength=nc)  # number of targets per class

        # p, r, ap50, ap = p[:, 0], r[:, 0], ap[:, 0], ap.mean(1)  # [P, R, AP@0.5, AP@0.5:0.95]
        # mp, mr, map50, map = p.mean(), r.mean(), ap50.mean(), ap.mean()
        # nt = np.bincount(stats[3].astype(np.int64), minlength=nc)  # number of targets per class
    else:
        nt = torch.zeros(1)

    # Print results
    pf = '%20s' + '%12.3g' * 6  # print format
    print(pf % ('all', seen, nt.sum(), mp, mr, map50, map))

    # Print speeds
    t = tuple(x / seen * 1E3 for x in (t0, t1, t0 + t1)) + (config_yolov5s['imgsz'][0], config_yolov5s['imgsz'][1], batch_size)  # tuple
    print('Speed: %.1f/%.1f/%.1f ms inference/NMS/total per %gx%g image at batch-size %g' % t)

    # Save JSON
    if save_json and len(jdict):
        f = 'detections_val2017_yolov5s_v4_0_results.json'  # filename
        print('\nCOCO mAP with pycocotools... saving %s...' % f)
        with open(f, 'w') as file:
            json.dump(jdict, file)

        try:  # https://github.com/cocodataset/cocoapi/blob/master/PythonAPI/pycocoEvalDemo.ipynb
            from pycocotools.coco import COCO
            from pycocotools.cocoeval import COCOeval

            imgIds = [int(Path(x).stem) for x in dataloader.dataset.img_files]
            cocoGt = COCO(glob.glob('/data/public_data/data_coco/annotations/instances_val*.json')[0])  # initialize COCO ground truth api
            cocoDt = cocoGt.loadRes(f)  # initialize COCO pred api
            cocoEval = COCOeval(cocoGt, cocoDt, 'bbox')
            cocoEval.params.imgIds = imgIds  # image IDs to evaluate
            cocoEval.evaluate()
            cocoEval.accumulate()
            cocoEval.summarize()
            map, map50 = cocoEval.stats[:2]  # update results (mAP@0.5:0.95, mAP@0.5)
        except Exception as e:
            print('ERROR: pycocotools unable to run: %s' % e)

    # Return results
    maps = np.zeros(nc) + map
    # for i, c in enumerate(ap_class):
    #     maps[c] = ap[i]
    # return (mp, mr, map50, map), maps, t
    return map50

@pytorch_model.register("haimiantai")  
def best(weight_path=None):
    # from pytorch.utils.experimental import attempt_load
    from qingwei.ts_utils.experimental import attempt_load
    
    if weight_path:
        model = attempt_load(weight_path,map_location=torch.device('cpu'))
    concrete_args = {"augment": False, "profile": False, "visualize": False, "val": True}
    name, _ = list(model.model.named_children())[-1]
    new_detect = DetectWithSigmoid()
    detect = getattr(model.model, name)
    new_detect.__dict__.update(detect.__dict__)
    setattr(model.model, name, new_detect)
    model_dict = {
        "model": model,
        "inputs": [torch.randn((1, 3, config_yolov5s['imgsz'][0], config_yolov5s['imgsz'][1]))],
        "concrete_args": concrete_args
    }
    return model_dict






