
1.创建容器镜像：sudo docker run --name qingwei -it -v /home/<USER>/Work/fankun/code/retrieve:/project 2fb060205791 /bin/bash
2.查看容器：sudo docker ps -a
3.启动容器：sudo docker start qingwei 
4.进入容器：sudo docker exec -it qingwei bin/bash     
5.         cd /project

1.模型量化
Knight --chip TX5336AV200 quant onnx -m Seed_2K_D_12CLS -f pytorch -if infer_yolov5_v6_0 --save-dir  /projectretrieve/models/Seed_2K_D_12CLS/Generation_1/best_model.pt -uds ./infer_yolov5_v6_0.py -i 50 -uis
Knight --chip TX5336AV200 quant onnx -m KuiHuaZi -f pytorch -if infer_yolov5_v6_0 --save-dir  /projectretrieve/models/Seed_2K_D_12CLS/Generation_1/best.pt -uds ./infer_yolov5_v6_0.py -i 50 -uis


2.模型编译
Knight --chip TX5336AV200  compile --onnx  /project/code/QingWei/Detect/yolov5-6.0/qingwei/KuiHuaZi/KuiHuaZi_quantize.onnx  --save-dir /project/code/QingWei/Detect/yolov5-6.0/qingwei/KuiHuaZi --hardware-resource-mode super --rgb

3.模型推理
./testKnight_3.elf ./model/QianShi_2048x384_yolov5n_7cls/qianshi_quantize_r.cfg ./model/QianShi_2048x384_yolov5n_7cls/qianshi_quantize_r.weight ./data/QianShi_2048x384_yolov5n_7cls/ 0 7 1
