
# YOLOv5 🚀 by Ultralytics, AGPL-3.0 license
# haimiantai dataset

# Train/val/test sets
val: /project/dataset/haimiantai_valsets/Docker_val.txt  # val images (Docker路径)

# Classes
nc: 11  # number of classes
names: # class names
      0: 'label1'
      1: 'label2'
      2: 'label3'
      3: 'label4'
      4: 'label5'
      5: 'label6'
      6: 'label7'
      7: 'label8'
      8: 'label9'
      9: 'label10'
      10: 'label11'