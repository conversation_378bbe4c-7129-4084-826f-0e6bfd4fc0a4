#!/bin/bash

# mobilenet
# ./autotrain nanguazi 0 1 48 128 128 0 0
# python autotrain.py Seed_384_2K_CLS_1000 0 1 48 128 128 0 0

# YOLO

export CUDA_VISIBLE_DEVICES=0
python ./autotrain.py haimiantai 0 0 1 4096 384 0 0

# sudo nice -n -10 /home/<USER>/anaconda3/envs/pytorch3.10/bin/python ./autotrain.py haimiantai 0 0 1 4096 384 0 0

# taskset -c 4-11 python ./autotrain.py haimiantai 0 0 1 4096 384 0 0


# python ./autotrain.py qianshi 0 0 1 2048 384 0 0

# model_arch: 0=YOLOv5N, 1=自动选择(高度72->YOLOv5N_Relu_s4s8, 高度192->YOLOv5N_Relu_16)

# 示例：
# 高度72时自动选择YOLOv5N_Relu_s4s8: python autotrain.py nanguazi 1 0 1 1024 72 1 0
# 高度192时自动选择YOLOv5N_Relu_16: python autotrain.py nanguazi 1 0 1 1024 192 1 0


# ####################### 榛子 ##########################
# ITEMS=100
# for ((i = 1; i <= $ITEMS; i++)); do
#     echo "第 $i 次运行程序"
#     rm -rf ./models/ZhenZhi_5CLS_20250426/*
#     python autotrain.py ZhenZhi_5CLS_20250426 0 1 48 128 128 0 0
#     scp -P 8898 ./models/ZhenZhi_5CLS_20250426/Generation_2/feature.dlc root@10.190.21.253:/data/simple_code/mobilenet/dlc_model/ZhenZhi_5CLS_20250422/
#     ssh -p 8898 root@10.190.21.253 "sh /root/liyuan/clone/git/tai-ho-vision_-mp/samples/temp_run.sh"
#     scp -P 8898 root@10.190.21.253:/root/liyuan/clone/git/tai-ho-vision_-mp/samples/log.txt /home/<USER>/work/lyh_file/retrieve/log.txt
# done
# #########################################################

# ####################### 芡实 ##########################
# ITEMS=100
# for ((i = 1; i <= $ITEMS; i++)); do
#     echo "第 $i 次运行程序"
#     rm -rf ./models/qianshi/*
#     python autotrain.py qianshi 0 1 48 128 128 0 0
#     scp -P 8898 ./models/qianshi/Generation_2/feature.dlc root@10.190.21.253:/data/simple_code/mobilenet/dlc_model/qianshi/
#     ssh -p 8898 root@10.190.21.253 "sh /root/liyuan/clone/git/tai-ho-vision_-mp/samples/temp_qianshi.sh"
#     scp -P 8898 root@10.190.21.253:/root/liyuan/clone/git/tai-ho-vision_-mp/samples/log_qianshi.txt /home/<USER>/work/lyh_file/retrieve/log_qianshi.txt
# done
# #######################################################

# ####################### 瓜子 ##########################
# ITEMS=100
# for ((i = 1; i <= $ITEMS; i++)); do
#     echo "第 $i 次运行程序"
#     rm -rf ./models/Seed_384_2K_CLS_1000/*
#     python autotrain.py Seed_384_2K_CLS_1000 0 1 48 128 128 0 0
#     scp -P 8898 ./models/Seed_384_2K_CLS_1000/Generation_2/feature.dlc root@10.190.21.253:/data/simple_code/mobilenet/dlc_model/Seed_384_2K_CLS_1000/
#     ssh -p 8898 root@10.190.21.253 "sh /root/liyuan/clone/git/tai-ho-vision_-mp/samples/temp_seed.sh"
#     scp -P 8898 root@10.190.21.253:/root/liyuan/clone/git/tai-ho-vision_-mp/samples/log_seed.txt /home/<USER>/work/lyh_file/retrieve/log_seed.txt
# done
# #######################################################


