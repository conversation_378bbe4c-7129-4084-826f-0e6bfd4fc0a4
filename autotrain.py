import os
import sys
import subprocess
from pathlib import Path
import argparse
import time
from tools.get_image_name import get_image_name
from tools.train import train
from tools.export_model import export_model
# from pytorch.configs.config_taiho import IMAGE_SIZE_LIST
import torch
import shutil

FOLDER_DICT = {0:"Generation_1", 1:"Generation_2"}
PROJECT_ROOT = os.getcwd()

def update_model(material_name, folder_name, platform, update_mode, infer_batch, image_size,
                 retain_size_info, model_dir, detection_model_id):

    ### 训练 #####  
    start_time = time.time()

    train_log, best_model, config = train(material_name, folder_name, update_mode, image_size, platform,
                                        retain_size_info, model_dir, detection_model_id)

    time_difference = time.time() - start_time

    hours, minutes, seconds = time_difference // 3600, (time_difference % 3600) // 60, time_difference % 60
    train_log.info(f"Train Cost Time: {int(hours)}hours {int(minutes)}minutes {int(seconds)}seconds")

    # # ### 模型转换 #####
    # export_model(best_model, config, PROJECT_ROOT, model_dir, material_name,
    #               folder_name, platform, infer_batch, image_size, train_log)

    # # ### 删除valsets文件夹 #####
    # if os.path.exists(config['Global']['val_path']):
    #      shutil.rmtree(config['Global']['val_path'])
    # outputLists = [item for item in os.listdir(model_dir) 
    #                if item != "feature.dlc" 
    #                and item != "RecModel"
    #                and not item.endswith(".weight")
    #                and not item.endswith(".cfg")]
    # for outputItem in outputLists:
    #     if os.path.exists(f"{model_dir}/{outputItem}"):
    #         if os.path.isfile(f"{model_dir}/{outputItem}"):
    #             os.remove(f"{model_dir}/{outputItem}")
    #         else:
    #             try:
    #                 shutil.rmtree(f"{model_dir}/{outputItem}")
    #             except OSError as e:
    #                 print(f"删除文件夹 {model_dir}/{outputItem} 时出错: {e}")

    train_log.info(f"update completely\n")
    torch.cuda.empty_cache()

def init_gpu():
    torch.cuda.empty_cache()

    # fuser - v / dev / nvidia *
    # kill - 9 PID 释放显卡内存

def check_empty(param, message):
    if param==None:
        print(message)
        sys.exit(1)

def main(material_name=None, platform=None, update_mode=None, infer_batch=None, image_width=1024, image_height=192,
         model_arch = 0, retain_size_info=0):

    check_empty(material_name, "未提供物料名称参数")
    check_empty(platform, "未指定模型平台名称")
    check_empty(update_mode, "未指定更新模式")
    check_empty(infer_batch, "未指定推理batch数目")
    check_empty(image_width, "未指定特征图尺寸")
    check_empty(image_height, "未指定特征图尺寸")
    update_mode = int(update_mode)
    infer_batch = int(infer_batch)
    platform = int(platform)
    model_arch = int(model_arch)
    image_size= [int(image_height), int(image_width)]
    retain_size_info = int(retain_size_info)
    folder_name = FOLDER_DICT[update_mode]

    # 当model_arch=1时，根据图像高度自动选择网络结构
    if model_arch == 1:
        if int(image_height) == 72:
            model_arch = 1  # YOLOv5N_Relu_s4s8
            print(f"检测到图像高度为72，自动选择模型架构: YOLOv5N_Relu_s4s8")
        elif int(image_height) == 192:
            model_arch = 2  # YOLOv5N_Relu_16
            print(f"检测到图像高度为192，自动选择模型架构: YOLOv5N_Relu_16")
        else:
            print(f"警告: 图像高度为{image_height}，不在预设范围内(72或192)，使用默认模型架构: YOLOv5N_Relu_s4s8")
            model_arch = 1  # 默认使用 YOLOv5N_Relu_s4s8

    model_dir = '{}/models/{}/{}'.format(PROJECT_ROOT, material_name, folder_name)
    Rec_model_dir = '{}/RecModel/'.format(model_dir)
    train_log = Path(Rec_model_dir) / "train.log"
    if not os.path.exists(Rec_model_dir):
        os.makedirs(Rec_model_dir)
    if train_log.exists():
        train_log.write_text(" > ")


    get_image_name(PROJECT_ROOT, material_name)

    update_model(material_name, folder_name, platform, update_mode, infer_batch, image_size,
                 retain_size_info, model_dir, model_arch)

if __name__ == "__main__":

    init_gpu()
    main(*sys.argv[1:])    # model_arch   0: "YOLOv5N",   1: 自动选择(高度72->YOLOv5N_Relu_s4s8, 高度192->YOLOv5N_Relu_16)
    # main(material_name="qianshi_yolov5_paste", platform=0, update_mode = 0,
    #      infer_batch=1, image_width=2048, image_height=384, model_arch = 0, retain_size_info=0)
    # main(material_name="mt1", platform=0, update_mode=1,
    #     infer_batch=32, image_width=128, image_height=128, model_arch=0, retain_size_info=0)

# sudo apt-get install libc++1
# 打包 pyinstaller --onefile snpe-onnx-to-dlc.py
# 加密打包： pyarmor -d gen --pack onefile autotrain.py
