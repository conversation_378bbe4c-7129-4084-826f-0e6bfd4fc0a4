 > Overriding model.nc=80 with nc=11

                 from  n    params  module                                  arguments                     
  0                -1  1      1760  pytorch.arch.backbone.yolov5.common.Conv[3, 16, 6, 2, 2]              
  1                -1  1      4672  pytorch.arch.backbone.yolov5.common.Conv[16, 32, 3, 2]                
  2                -1  1      4800  pytorch.arch.backbone.yolov5.common.C3  [32, 32, 1]                   
  3                -1  1     18560  pytorch.arch.backbone.yolov5.common.Conv[32, 64, 3, 2]                
  4                -1  2     29184  pytorch.arch.backbone.yolov5.common.C3  [64, 64, 2]                   
  5                -1  1     73984  pytorch.arch.backbone.yolov5.common.Conv[64, 128, 3, 2]               
  6                -1  3    156928  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 3]                 
  7                -1  1    295424  pytorch.arch.backbone.yolov5.common.Conv[128, 256, 3, 2]              
  8                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1]                 
  9                -1  1    164608  pytorch.arch.backbone.yolov5.common.SPPF[256, 256, 5]                 
 10                -1  1     33024  pytorch.arch.backbone.yolov5.common.Conv[256, 128, 1, 1]              
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 12           [-1, 6]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 13                -1  1     90880  pytorch.arch.backbone.yolov5.common.C3  [256, 128, 1, False]          
 14                -1  1      8320  pytorch.arch.backbone.yolov5.common.Conv[128, 64, 1, 1]               
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 16           [-1, 4]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 17                -1  1     22912  pytorch.arch.backbone.yolov5.common.C3  [128, 64, 1, False]           
 18                -1  1     36992  pytorch.arch.backbone.yolov5.common.Conv[64, 64, 3, 2]                
 19          [-1, 14]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 20                -1  1     74496  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 1, False]          
 21                -1  1    147712  pytorch.arch.backbone.yolov5.common.Conv[128, 128, 3, 2]              
 22          [-1, 10]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 23                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1, False]          
 24      [17, 20, 23]  1     21648  pytorch.arch.backbone.yolov5.yolo.Detect[11, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [64, 128, 256]]
Model Summary: 270 layers, 1778800 parameters, 1778800 gradients

len(pre_dict): 343
missing_keys: ['model.24.m.0.weight', 'model.24.m.0.bias', 'model.24.m.1.weight', 'model.24.m.1.bias', 'model.24.m.2.weight', 'model.24.m.2.bias']
Generated 1000 validation images in /home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai_valsets
1000 found, 0 missing, 0 empty, 0 corrupted

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       1/5     2.69G   0.05573   0.07944   0.05787       384      4096     25.1%
eta: 0:20:52

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       2/5     2.69G   0.04067   0.06571   0.03912       384      4096     25.2%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16547        0.4      0.673      0.561      0.277
              label1       1000       1460      0.388      0.447      0.336      0.132
              label2       1000       1553      0.308      0.332      0.284      0.185
              label3       1000       1504      0.235       0.32      0.227       0.14
              label4       1000       1528      0.218      0.259       0.24      0.157
              label5       1000       1488      0.473      0.553      0.475      0.256
              label6       1000       1485      0.375      0.994      0.896      0.527
              label7       1000       1497       0.42      0.927       0.72      0.304
              label8       1000       1502      0.464      0.883      0.656      0.243
              label9       1000       1481      0.565      0.971      0.927      0.421
             label10       1000       1532      0.511      0.988      0.856      0.361
             label11       1000       1517      0.449      0.732      0.556      0.318
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:05:58

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       3/5     2.69G   0.03553   0.06097   0.02518       384      4096     25.4%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16547      0.464      0.788       0.66      0.422
              label1       1000       1460       0.37      0.529      0.436      0.262
              label2       1000       1553      0.338      0.584      0.342      0.266
              label3       1000       1504      0.244      0.561      0.266       0.17
              label4       1000       1528      0.297      0.543      0.313      0.235
              label5       1000       1488      0.606      0.752      0.662      0.305
              label6       1000       1485      0.402      0.985      0.884      0.659
              label7       1000       1497      0.731      0.921      0.927      0.548
              label8       1000       1502      0.447      0.861       0.67      0.476
              label9       1000       1481      0.664      0.963      0.946      0.596
             label10       1000       1532      0.621      0.981      0.909      0.518
             label11       1000       1517      0.377      0.984      0.907      0.606
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:04:28

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       4/5     2.69G   0.02571   0.04983   0.01777       384      4096     25.4%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16547      0.748      0.805      0.809      0.603
              label1       1000       1460      0.494      0.585      0.592      0.392
              label2       1000       1553      0.504      0.606      0.542      0.434
              label3       1000       1504      0.426      0.632      0.522      0.412
              label4       1000       1528      0.494      0.614      0.569       0.46
              label5       1000       1488      0.826      0.853      0.917      0.694
              label6       1000       1485       0.96      0.916      0.977      0.832
              label7       1000       1497      0.858      0.936      0.929      0.624
              label8       1000       1502      0.813      0.825      0.902       0.63
              label9       1000       1481      0.956      0.976      0.981      0.695
             label10       1000       1532      0.944      0.962       0.98      0.641
             label11       1000       1517      0.955      0.954      0.985      0.823
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:02:58
