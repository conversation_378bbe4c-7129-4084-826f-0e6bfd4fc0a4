 > Overriding model.nc=80 with nc=11

                 from  n    params  module                                  arguments                     
  0                -1  1      1760  pytorch.arch.backbone.yolov5.common.Conv[3, 16, 6, 2, 2]              
  1                -1  1      4672  pytorch.arch.backbone.yolov5.common.Conv[16, 32, 3, 2]                
  2                -1  1      4800  pytorch.arch.backbone.yolov5.common.C3  [32, 32, 1]                   
  3                -1  1     18560  pytorch.arch.backbone.yolov5.common.Conv[32, 64, 3, 2]                
  4                -1  2     29184  pytorch.arch.backbone.yolov5.common.C3  [64, 64, 2]                   
  5                -1  1     73984  pytorch.arch.backbone.yolov5.common.Conv[64, 128, 3, 2]               
  6                -1  3    156928  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 3]                 
  7                -1  1    295424  pytorch.arch.backbone.yolov5.common.Conv[128, 256, 3, 2]              
  8                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1]                 
  9                -1  1    164608  pytorch.arch.backbone.yolov5.common.SPPF[256, 256, 5]                 
 10                -1  1     33024  pytorch.arch.backbone.yolov5.common.Conv[256, 128, 1, 1]              
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 12           [-1, 6]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 13                -1  1     90880  pytorch.arch.backbone.yolov5.common.C3  [256, 128, 1, False]          
 14                -1  1      8320  pytorch.arch.backbone.yolov5.common.Conv[128, 64, 1, 1]               
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 16           [-1, 4]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 17                -1  1     22912  pytorch.arch.backbone.yolov5.common.C3  [128, 64, 1, False]           
 18                -1  1     36992  pytorch.arch.backbone.yolov5.common.Conv[64, 64, 3, 2]                
 19          [-1, 14]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 20                -1  1     74496  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 1, False]          
 21                -1  1    147712  pytorch.arch.backbone.yolov5.common.Conv[128, 128, 3, 2]              
 22          [-1, 10]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 23                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1, False]          
 24      [17, 20, 23]  1     21648  pytorch.arch.backbone.yolov5.yolo.Detect[11, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [64, 128, 256]]
Model Summary: 270 layers, 1778800 parameters, 1778800 gradients

len(pre_dict): 343
missing_keys: ['model.24.m.0.weight', 'model.24.m.0.bias', 'model.24.m.1.weight', 'model.24.m.1.bias', 'model.24.m.2.weight', 'model.24.m.2.bias']
Generated 1000 validation images in /home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai_valsets
1000 found, 0 missing, 0 empty, 0 corrupted

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       1/3     2.82G   0.05546   0.08193   0.05737       384      4096     23.2%
eta: 0:05:07

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       2/3     2.82G   0.03899   0.06446   0.03952       384      4096     23.2%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16461      0.375      0.704      0.577      0.335
              label1       1000       1499       0.36      0.517      0.408      0.182
              label2       1000       1424      0.318      0.496      0.295      0.216
              label3       1000       1481      0.268      0.401      0.257      0.166
              label4       1000       1543      0.247      0.379      0.251      0.189
              label5       1000       1533      0.469      0.729      0.676      0.418
              label6       1000       1461      0.534      0.984      0.908      0.653
              label7       1000       1492      0.378      0.878      0.761      0.421
              label8       1000       1533      0.377      0.837      0.709      0.259
              label9       1000       1497      0.281      0.978      0.788      0.429
             label10       1000       1480      0.507      0.963      0.891      0.502
             label11       1000       1518      0.389      0.582      0.397      0.252
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:02:39

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       3/3     3.22G   0.02975   0.05487   0.02826       384      4096     23.4%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16461      0.602      0.794      0.699        0.5
              label1       1000       1499      0.484      0.621      0.568      0.422
              label2       1000       1424      0.307      0.604      0.318      0.239
              label3       1000       1481      0.255      0.542      0.277      0.212
              label4       1000       1543      0.286      0.539      0.303       0.25
              label5       1000       1533      0.692       0.84      0.854      0.642
              label6       1000       1461      0.824      0.987      0.973      0.801
              label7       1000       1492      0.758      0.936      0.932      0.633
              label8       1000       1533        0.8      0.916       0.93       0.65
              label9       1000       1497      0.917      0.963      0.974      0.598
             label10       1000       1480      0.928      0.978      0.981      0.627
             label11       1000       1518      0.368      0.812      0.577      0.423
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:01:17
Save Last model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/last_model.pth
Finish Training
best metric: mAP@.5:69.89, mAP@.5:.95:49.97
Train Cost Time: 0hours 6minutes 7seconds
update completely

