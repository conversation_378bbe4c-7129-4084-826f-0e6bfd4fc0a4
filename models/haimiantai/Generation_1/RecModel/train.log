 > Overriding model.nc=80 with nc=11

                 from  n    params  module                                  arguments                     
  0                -1  1      1760  pytorch.arch.backbone.yolov5.common.Conv[3, 16, 6, 2, 2]              
  1                -1  1      4672  pytorch.arch.backbone.yolov5.common.Conv[16, 32, 3, 2]                
  2                -1  1      4800  pytorch.arch.backbone.yolov5.common.C3  [32, 32, 1]                   
  3                -1  1     18560  pytorch.arch.backbone.yolov5.common.Conv[32, 64, 3, 2]                
  4                -1  2     29184  pytorch.arch.backbone.yolov5.common.C3  [64, 64, 2]                   
  5                -1  1     73984  pytorch.arch.backbone.yolov5.common.Conv[64, 128, 3, 2]               
  6                -1  3    156928  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 3]                 
  7                -1  1    295424  pytorch.arch.backbone.yolov5.common.Conv[128, 256, 3, 2]              
  8                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1]                 
  9                -1  1    164608  pytorch.arch.backbone.yolov5.common.SPPF[256, 256, 5]                 
 10                -1  1     33024  pytorch.arch.backbone.yolov5.common.Conv[256, 128, 1, 1]              
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 12           [-1, 6]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 13                -1  1     90880  pytorch.arch.backbone.yolov5.common.C3  [256, 128, 1, False]          
 14                -1  1      8320  pytorch.arch.backbone.yolov5.common.Conv[128, 64, 1, 1]               
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 16           [-1, 4]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 17                -1  1     22912  pytorch.arch.backbone.yolov5.common.C3  [128, 64, 1, False]           
 18                -1  1     36992  pytorch.arch.backbone.yolov5.common.Conv[64, 64, 3, 2]                
 19          [-1, 14]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 20                -1  1     74496  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 1, False]          
 21                -1  1    147712  pytorch.arch.backbone.yolov5.common.Conv[128, 128, 3, 2]              
 22          [-1, 10]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 23                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1, False]          
 24      [17, 20, 23]  1     21648  pytorch.arch.backbone.yolov5.yolo.Detect[11, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [64, 128, 256]]
Model Summary: 270 layers, 1778800 parameters, 1778800 gradients

len(pre_dict): 343
missing_keys: ['model.24.m.0.weight', 'model.24.m.0.bias', 'model.24.m.1.weight', 'model.24.m.1.bias', 'model.24.m.2.weight', 'model.24.m.2.bias']
Generated 1000 validation images in /home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai_valsets
1000 found, 0 missing, 0 empty, 0 corrupted

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       1/3     2.71G   0.05588   0.07599   0.05651       384      4096     25.2%
eta: 0:14:43

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       2/3     2.71G   0.03972   0.06399   0.03943       384      4096     25.2%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16405      0.298      0.664      0.473      0.213
              label1       1000       1480       0.28      0.539      0.373     0.0916
              label2       1000       1523      0.257      0.502      0.258      0.175
              label3       1000       1497      0.216      0.276      0.212       0.14
              label4       1000       1491        0.3      0.503      0.288      0.178
              label5       1000       1467      0.456      0.632        0.6      0.339
              label6       1000       1488      0.412      0.967      0.758      0.458
              label7       1000       1482      0.197      0.895      0.528      0.208
              label8       1000       1456      0.294      0.839      0.601      0.168
              label9       1000       1547      0.222      0.983      0.664      0.179
             label10       1000       1463      0.307      0.919      0.654      0.234
             label11       1000       1511      0.334      0.248      0.268      0.174
