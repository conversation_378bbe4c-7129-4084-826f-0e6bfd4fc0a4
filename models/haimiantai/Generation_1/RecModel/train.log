 > Overriding model.nc=80 with nc=11

                 from  n    params  module                                  arguments                     
  0                -1  1      1760  pytorch.arch.backbone.yolov5.common.Conv[3, 16, 6, 2, 2]              
  1                -1  1      4672  pytorch.arch.backbone.yolov5.common.Conv[16, 32, 3, 2]                
  2                -1  1      4800  pytorch.arch.backbone.yolov5.common.C3  [32, 32, 1]                   
  3                -1  1     18560  pytorch.arch.backbone.yolov5.common.Conv[32, 64, 3, 2]                
  4                -1  2     29184  pytorch.arch.backbone.yolov5.common.C3  [64, 64, 2]                   
  5                -1  1     73984  pytorch.arch.backbone.yolov5.common.Conv[64, 128, 3, 2]               
  6                -1  3    156928  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 3]                 
  7                -1  1    295424  pytorch.arch.backbone.yolov5.common.Conv[128, 256, 3, 2]              
  8                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1]                 
  9                -1  1    164608  pytorch.arch.backbone.yolov5.common.SPPF[256, 256, 5]                 
 10                -1  1     33024  pytorch.arch.backbone.yolov5.common.Conv[256, 128, 1, 1]              
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 12           [-1, 6]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 13                -1  1     90880  pytorch.arch.backbone.yolov5.common.C3  [256, 128, 1, False]          
 14                -1  1      8320  pytorch.arch.backbone.yolov5.common.Conv[128, 64, 1, 1]               
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 16           [-1, 4]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 17                -1  1     22912  pytorch.arch.backbone.yolov5.common.C3  [128, 64, 1, False]           
 18                -1  1     36992  pytorch.arch.backbone.yolov5.common.Conv[64, 64, 3, 2]                
 19          [-1, 14]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 20                -1  1     74496  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 1, False]          
 21                -1  1    147712  pytorch.arch.backbone.yolov5.common.Conv[128, 128, 3, 2]              
 22          [-1, 10]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 23                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1, False]          
 24      [17, 20, 23]  1     21648  pytorch.arch.backbone.yolov5.yolo.Detect[11, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [64, 128, 256]]
Model Summary: 270 layers, 1778800 parameters, 1778800 gradients

len(pre_dict): 343
missing_keys: ['model.24.m.0.weight', 'model.24.m.0.bias', 'model.24.m.1.weight', 'model.24.m.1.bias', 'model.24.m.2.weight', 'model.24.m.2.bias']
Generated 1000 validation images in /home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai_valsets
1000 found, 0 missing, 0 empty, 0 corrupted

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       1/3     2.71G   0.05588   0.07599   0.05651       384      4096     25.2%
eta: 0:14:43
