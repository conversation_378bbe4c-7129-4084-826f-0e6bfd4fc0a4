 > Overriding model.nc=80 with nc=11

                 from  n    params  module                                  arguments                     
  0                -1  1      1760  pytorch.arch.backbone.yolov5.common.Conv[3, 16, 6, 2, 2]              
  1                -1  1      4672  pytorch.arch.backbone.yolov5.common.Conv[16, 32, 3, 2]                
  2                -1  1      4800  pytorch.arch.backbone.yolov5.common.C3  [32, 32, 1]                   
  3                -1  1     18560  pytorch.arch.backbone.yolov5.common.Conv[32, 64, 3, 2]                
  4                -1  2     29184  pytorch.arch.backbone.yolov5.common.C3  [64, 64, 2]                   
  5                -1  1     73984  pytorch.arch.backbone.yolov5.common.Conv[64, 128, 3, 2]               
  6                -1  3    156928  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 3]                 
  7                -1  1    295424  pytorch.arch.backbone.yolov5.common.Conv[128, 256, 3, 2]              
  8                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1]                 
  9                -1  1    164608  pytorch.arch.backbone.yolov5.common.SPPF[256, 256, 5]                 
 10                -1  1     33024  pytorch.arch.backbone.yolov5.common.Conv[256, 128, 1, 1]              
 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 12           [-1, 6]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 13                -1  1     90880  pytorch.arch.backbone.yolov5.common.C3  [256, 128, 1, False]          
 14                -1  1      8320  pytorch.arch.backbone.yolov5.common.Conv[128, 64, 1, 1]               
 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          
 16           [-1, 4]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 17                -1  1     22912  pytorch.arch.backbone.yolov5.common.C3  [128, 64, 1, False]           
 18                -1  1     36992  pytorch.arch.backbone.yolov5.common.Conv[64, 64, 3, 2]                
 19          [-1, 14]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 20                -1  1     74496  pytorch.arch.backbone.yolov5.common.C3  [128, 128, 1, False]          
 21                -1  1    147712  pytorch.arch.backbone.yolov5.common.Conv[128, 128, 3, 2]              
 22          [-1, 10]  1         0  pytorch.arch.backbone.yolov5.common.Concat[1]                           
 23                -1  1    296448  pytorch.arch.backbone.yolov5.common.C3  [256, 256, 1, False]          
 24      [17, 20, 23]  1     21648  pytorch.arch.backbone.yolov5.yolo.Detect[11, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [64, 128, 256]]
Model Summary: 270 layers, 1778800 parameters, 1778800 gradients

len(pre_dict): 343
missing_keys: ['model.24.m.0.weight', 'model.24.m.0.bias', 'model.24.m.1.weight', 'model.24.m.1.bias', 'model.24.m.2.weight', 'model.24.m.2.bias']
Generated 1000 validation images in /home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai_valsets
1000 found, 0 missing, 0 empty, 0 corrupted

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       1/3     2.82G   0.05676   0.08076   0.05496       384      4096     22.3%
eta: 0:04:50

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       2/3     2.82G   0.04134   0.06449   0.03599       384      4096     22.3%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16498      0.442      0.745      0.628      0.347
              label1       1000       1481      0.347      0.487      0.367      0.173
              label2       1000       1490      0.341      0.398      0.324      0.204
              label3       1000       1456      0.312      0.223      0.279      0.169
              label4       1000       1517      0.348      0.719      0.422      0.262
              label5       1000       1518      0.559      0.846      0.752      0.407
              label6       1000       1511      0.567      0.941       0.88      0.521
              label7       1000       1492      0.606      0.897      0.836      0.483
              label8       1000       1529      0.445      0.898      0.784      0.484
              label9       1000       1492       0.47      0.942       0.76      0.365
             label10       1000       1501      0.545      0.969      0.901      0.469
             label11       1000       1511      0.318      0.869      0.606      0.274
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:02:44

     Epoch   gpu_mem       box       obj       cls     img_h     img_w       RAM
       3/3     3.22G   0.03121   0.05535   0.02341       384      4096     22.5%
               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95
                 all       1000      16498      0.712      0.813      0.797      0.604
              label1       1000       1481      0.561      0.521      0.561      0.416
              label2       1000       1490      0.769      0.458      0.624       0.52
              label3       1000       1456      0.339      0.565      0.363      0.288
              label4       1000       1517      0.453      0.844      0.619      0.546
              label5       1000       1518      0.746      0.888      0.911      0.714
              label6       1000       1511       0.86      0.974      0.965      0.812
              label7       1000       1492      0.805      0.927      0.894      0.589
              label8       1000       1529      0.792      0.886      0.933      0.721
              label9       1000       1492      0.955      0.932      0.967      0.646
             label10       1000       1501       0.93      0.969      0.988       0.67
             label11       1000       1511      0.622      0.981      0.945      0.716
Save Best model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/best_model.pt
eta: 0:01:22
Save Last model in path /home/<USER>/work/lyh_file/retrieve_cpu_speed/models/haimiantai/Generation_1/last_model.pth
Finish Training
best metric: mAP@.5:79.72, mAP@.5:.95:60.35
Train Cost Time: 0hours 6minutes 5seconds
update completely

