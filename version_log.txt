2025年6月26日 修改内容如下：
1.yolo训练代码算子Relu6改为Relu；
2.yolo训练更新随机种子seed = 0, cudnn.benchmark=False, cudnn.deterministic=True；
3.修改yolo训练weight_decay；
4.修改yolo训练增益hpy[‘obj’] = 1*3/nl 改为hpy[‘obj’] = (imgsz[0]/640)* (imgsz[1]/640)*3/nl ；
5.修改yolo训练optimzer.zero_grad和scaler.step()位置更改，由每个step改成在梯度累积内部；
6.修改yolo训练imgs.to(device)改为imgs.to(device, non_blocking=True)异步传输：启用 non_blocking=True 后，数据传输(CPU 到 GPU)不会阻塞当前线程，程序会立即继续执行后续代码，而数据传输在后台进行。
7.yolo训练超参外放。

2025年6月24日 修改内容如下：
1.修改清微删除逻辑保留RecModel文件夹及其子文件夹

2025年6月24日 修改内容如下：
1.新增清微模型选择，当model_arch=1时，根据图像高度自动选择网络结构，高度72选择YOLOv5N_Relu_s4s8，高度192选择YOLOv5N_Relu_16

2025年6月23日 修改内容如下：
1.pytorch/arch/backbone/yolov5/yolov5_arch.py,新增  3: "YOLOv5N_Relu_8",4: "YOLOv5N_Relu_16",5: "YOLOv5N_Relu_8_16"三个模型，更新pytorch/configs/model_list.py模型列表
2.新增清微将anchors写入cfg文件功能

2025年6月20日 修改内容如下：
1. 新增tools/detect_yolov5.py文件，yolov5模型对图片进行画框识别和切小图操作，同时修改对应的common.py和general.py文件

2025年5月22日 修改内容如下：
1. get_image_name新增排序custom_sort函数，第43行类别名称排序有单纯依据字符串改为依据custom_sort函数——> subfolders_list.sort()改为subfolders_list.sort(key=custom_sort)， 解决"label10"排在"label2"之前的问题

2025年5月09日 修改内容如下：
1. 更新清微多个模型可用板块；
2. 更新加密脚本encrypt.sh ；

2025年5月06日 修改内容如下：
1. 加入libc++1库判断安装模块；
2. 删减不必要的python库；


2025年4月30日 修改内容如下：
1. 加入清微模型转换模块 ;


2025年4月22日 修改内容如下：
1. 一代yolov5增加best_map50与best_map(0.5:0.95) ;
2. 加入mobilenet_v4_conv_small ;
3. 判断条件由 结构 改成 update_mode ;
4. 分类模型dataloader加入drop_last=True,对不满64batch的末位数据进行丢弃 避免batch size为1的数据在BN时报错。



2025年4月22日 修改内容如下：
1. 还原机器学习评估，机器学习评估前后添加随机状态记录和随机状态还原, 并且在调用阶段固定随机种子；
2. 采用Paddle版本预训练权重，训练阶段采用CLIP限制权重范围，DWConv.clip(-0.2,+0.2), Conv.clip(-1,+1), BN.clip(-1,+1), 量化后模型掉点在0.5以内，满足设计要求；
3. 更改错误的ColorJitter代码，旋转与ColorJitter可加入数据增强。


2025年4月12日 修改内容如下：
1. 实例粘贴图片名称由具体坐标更改未T,B,L,R,M的记号表示；
2. 模式1更新的分类模型数据加载dataloader更改为抽样更均匀的ForeverDataloader形式。


2025年4月11日 修改内容如下：
1. 取消机器学习评估，取消分类模型mobilenet权重。