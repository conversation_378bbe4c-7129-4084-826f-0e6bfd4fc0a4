import numpy as np
from multiprocessing import Array
from multiprocessing import Manager
from torch.utils.data import DataLoader, Dataset
import os
import cv2
import torch
import cv2
from pytorch.engine.engine import ENGINE
from pytorch.configs.config import update_config
import time
from tqdm import tqdm
from PIL import Image


class MyDataset(Dataset):
    def __init__(self, instance_gallery, background_folder, imgsz):
        mgr = Manager()
        self.epoch_batch = mgr.list()
        # self.epoch_batch.append('Python')
        self.mgr_dict = mgr.dict()
        self.imgsz = imgsz
        self.background_folder = [os.path.join(background_folder,Name) for Name in os.listdir(background_folder)]
        self.background_img = []
        for i in range(min(len(self.background_folder), 350)):
            self.background_img.append(cv2.resize(cv2.imread(self.background_folder[i]), dsize=(self.imgsz[1], self.imgsz[0])))
        # self.img_data = np.frombuffer(shared_array, dtype=np.uint8).reshape(N, H, W, C)


    def __len__(self):
        return 3
    
    def __getitem__(self, index):
        print("len(self.epoch_batch):",len(self.epoch_batch))
        # print("self.mgr_list[0]:",self.mgr_list[0])
        # print("self.mgr_list[1]:",self.mgr_list[1])
        # print("self.mgr_dict:",self.mgr_dict)
        img = self.background_img[index]     
        img = torch.from_numpy(img)
        return img

    @staticmethod
    def collate_fn(batch):
        img = batch  # transposed
        return torch.stack(img, 0)


if __name__ == "__main__":
    DATASET_DIR = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai"  # 数据集根目录
    background_folder = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/background"
    imgsz = [384, 4096]
    batch_size = 8
    train_size = 9000
    
    engine = ENGINE()
    __project__ = os.getcwd()
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_folder}/Generation_1")

    train_instance_path = engine.config['DataLoader']['Train']['dataset']['cls_label_path']
    class_names = engine.config['Global']['cls_name']
    

    from pytorch.data.dataloader.create_dataloader import LoadImagesAndLabelsInstanceAUG
    dataset = LoadImagesAndLabelsInstanceAUG(train_instance_path, 
                                            class_names, 
                                            batch_size, 
                                            imgsz,
                                            augment=True,  # augmentation
                                            hyp=engine.config,  # hyperparameters
                                            single_cls=False,
                                            train_size = train_size,
                                            background_path = background_folder)
    
    ###### 必须用DataLoader ， 不能用InfiniteDataLoader ##############
    dataloader = DataLoader(dataset,
                            batch_size=8,
                            shuffle=True,
                            num_workers=8,  
                            sampler=None,
                            pin_memory=True,
                            collate_fn=LoadImagesAndLabelsInstanceAUG.collate_fn)

    # dataset = MyDataset(train_instance_path, background_folder, imgsz)
    # dataloader = DataLoader(dataset,
    #                                 batch_size=8,
    #                                 shuffle=True,
    #                                 num_workers=8,  
    #                                 sampler=None,
    #                                 pin_memory=True,
    #                                 collate_fn=MyDataset.collate_fn)


    print("begin optimized_epoch_batch") 
    epoch_batch_start = time.time()
    from pytorch.data.dataloader.speed_dataloader import optimized_epoch_batch
    epoch_batch = optimized_epoch_batch(dataset.instance_gallery, imgsz, num_batches=9000, num_processes=8)
    print(f"optimized_epoch_batch耗时: {(time.time() - epoch_batch_start):.4f} 秒")
    print("len(epoch_batch)",len(epoch_batch))
    dataset.epoch_batch=epoch_batch

    device = torch.device("cuda")
    start = time.time()
    for i in range(3):
        for inputs, labels in tqdm(dataloader, total=len(dataloader)):
            inputs, labels = inputs.to(device, non_blocking=True).float()/255, labels.to(device, non_blocking=True)
    print(f"dataloader耗时: {(time.time() - start):.4f} 秒")