#!/usr/bin/env python3
"""
Performance benchmark for bbox_iou optimization with JIT compilation.
"""

import time
import torch
import numpy as np
from pytorch.utils.general import bbox_iou, bbox_iou_jit, box_iou


def create_test_data(num_boxes1=1000, num_boxes2=1000, device='cpu'):
    """Create test data for benchmarking."""
    # Generate random boxes in xywh format
    boxes1_xywh = torch.rand(num_boxes1, 4, device=device) * 100
    boxes2_xywh = torch.rand(num_boxes2, 4, device=device) * 100
    
    # Convert to x1y1x2y2 format
    boxes1_xyxy = torch.stack([
        boxes1_xywh[:, 0] - boxes1_xywh[:, 2] / 2,
        boxes1_xywh[:, 1] - boxes1_xywh[:, 3] / 2,
        boxes1_xywh[:, 0] + boxes1_xywh[:, 2] / 2,
        boxes1_xywh[:, 1] + boxes1_xywh[:, 3] / 2
    ], dim=1)
    
    boxes2_xyxy = torch.stack([
        boxes2_xywh[:, 0] - boxes2_xywh[:, 2] / 2,
        boxes2_xywh[:, 1] - boxes2_xywh[:, 3] / 2,
        boxes2_xywh[:, 0] + boxes2_xywh[:, 2] / 2,
        boxes2_xywh[:, 1] + boxes2_xywh[:, 3] / 2
    ], dim=1)
    
    return boxes1_xywh, boxes2_xywh, boxes1_xyxy, boxes2_xyxy


def benchmark_function(func, *args, num_runs=100, warmup_runs=10):
    """Benchmark a function with warmup."""
    # Warmup runs
    for _ in range(warmup_runs):
        _ = func(*args)
    
    # Synchronize GPU if using CUDA
    if torch.cuda.is_available() and args[0].is_cuda:
        torch.cuda.synchronize()
    
    # Actual benchmark
    start_time = time.time()
    for _ in range(num_runs):
        result = func(*args)
    
    if torch.cuda.is_available() and args[0].is_cuda:
        torch.cuda.synchronize()
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs
    return avg_time, result


def original_bbox_iou(box1, box2, x1y1x2y2=True, eps=1e-7):
    """Original bbox_iou implementation for comparison."""
    box2 = box2.T
    if x1y1x2y2:
        b1_x1, b1_y1, b1_x2, b1_y2 = box1[0], box1[1], box1[2], box1[3]
        b2_x1, b2_y1, b2_x2, b2_y2 = box2[0], box2[1], box2[2], box2[3]
    else:
        b1_x1, b1_x2 = box1[0] - box1[2] / 2, box1[0] + box1[2] / 2
        b1_y1, b1_y2 = box1[1] - box1[3] / 2, box1[1] + box1[3] / 2
        b2_x1, b2_x2 = box2[0] - box2[2] / 2, box2[0] + box2[2] / 2
        b2_y1, b2_y2 = box2[1] - box2[3] / 2, box2[1] + box2[3] / 2
    
    # Intersection area
    inter = (torch.min(b1_x2, b2_x2) - torch.max(b1_x1, b2_x1)).clamp(0) * \
            (torch.min(b1_y2, b2_y2) - torch.max(b1_y1, b2_y1)).clamp(0)
    
    # Union Area
    w1, h1 = b1_x2 - b1_x1, b1_y2 - b1_y1 + eps
    w2, h2 = b2_x2 - b2_x1, b2_y2 - b2_y1 + eps
    union = w1 * h1 + w2 * h2 - inter + eps
    
    return inter / union


def run_benchmark():
    """Run comprehensive benchmark comparing different implementations."""
    print("=" * 80)
    print("BBOX IoU Performance Benchmark")
    print("=" * 80)
    
    # Test configurations
    test_configs = [
        (100, 100, "Small"),
        (500, 500, "Medium"),
        (1000, 1000, "Large"),
        (2000, 2000, "Extra Large")
    ]
    
    devices = ['cpu']
    if torch.cuda.is_available():
        devices.append('cuda')
    
    for device in devices:
        print(f"\n{'='*20} Device: {device.upper()} {'='*20}")
        
        for num_boxes1, num_boxes2, size_name in test_configs:
            print(f"\n--- {size_name} Test: {num_boxes1}x{num_boxes2} boxes ---")
            
            # Create test data
            boxes1_xywh, boxes2_xywh, boxes1_xyxy, boxes2_xyxy = create_test_data(
                num_boxes1, num_boxes2, device
            )
            
            # Test single box vs multiple boxes (common use case)
            single_box_xywh = boxes1_xywh[0]
            single_box_xyxy = boxes1_xyxy[0]
            
            results = {}
            
            # 1. Original implementation (single box vs multiple)
            try:
                time_orig, result_orig = benchmark_function(
                    original_bbox_iou, single_box_xyxy, boxes2_xyxy, True
                )
                results['Original (x1y1x2y2)'] = time_orig
                print(f"Original bbox_iou (x1y1x2y2):     {time_orig*1000:.3f} ms")
            except Exception as e:
                print(f"Original bbox_iou failed: {e}")
            
            # 2. Current optimized implementation
            try:
                time_opt, result_opt = benchmark_function(
                    bbox_iou, single_box_xyxy, boxes2_xyxy, True
                )
                results['Optimized (x1y1x2y2)'] = time_opt
                print(f"Optimized bbox_iou (x1y1x2y2):    {time_opt*1000:.3f} ms")
            except Exception as e:
                print(f"Optimized bbox_iou failed: {e}")
            
            # 3. JIT compiled version
            try:
                time_jit, result_jit = benchmark_function(
                    bbox_iou_jit, single_box_xyxy.unsqueeze(0), boxes2_xyxy
                )
                results['JIT Compiled'] = time_jit
                print(f"JIT compiled bbox_iou:            {time_jit*1000:.3f} ms")
            except Exception as e:
                print(f"JIT compiled bbox_iou failed: {e}")
            
            # 4. Direct box_iou function
            try:
                time_box_iou, result_box_iou = benchmark_function(
                    box_iou, single_box_xyxy.unsqueeze(0), boxes2_xyxy
                )
                results['Direct box_iou'] = time_box_iou
                print(f"Direct box_iou:                   {time_box_iou*1000:.3f} ms")
            except Exception as e:
                print(f"Direct box_iou failed: {e}")
            
            # Calculate speedups
            if 'Original (x1y1x2y2)' in results:
                baseline = results['Original (x1y1x2y2)']
                print(f"\nSpeedup vs Original:")
                for name, time_val in results.items():
                    if name != 'Original (x1y1x2y2)':
                        speedup = baseline / time_val
                        print(f"  {name:25}: {speedup:.2f}x faster")
            
            # Verify results are similar (within tolerance)
            if len(results) >= 2:
                print(f"\nResult verification:")
                result_list = []
                names = []
                for name in ['Original (x1y1x2y2)', 'Optimized (x1y1x2y2)', 'JIT Compiled', 'Direct box_iou']:
                    if name in results:
                        if name == 'Original (x1y1x2y2)':
                            result_list.append(result_orig)
                        elif name == 'Optimized (x1y1x2y2)':
                            result_list.append(result_opt)
                        elif name == 'JIT Compiled':
                            result_list.append(result_jit.squeeze(0))
                        elif name == 'Direct box_iou':
                            result_list.append(result_box_iou.squeeze(0))
                        names.append(name)
                
                if len(result_list) >= 2:
                    for i in range(1, len(result_list)):
                        diff = torch.abs(result_list[0] - result_list[i]).max().item()
                        print(f"  Max diff {names[0]} vs {names[i]}: {diff:.6f}")


if __name__ == "__main__":
    run_benchmark()
