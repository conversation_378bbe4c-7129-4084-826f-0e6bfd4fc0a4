#!/bin/bash

# 检查参数是否为空
check_empty() {
    if [ -z "$1" ]; then
        echo "$2"
        exit 1
    fi
}


# 主程序
main() {
    check_empty "$1" "请提供版本号, Usage: $0 版本号"

    pyarmor g -O ./retrieve/ -r ./ --exclude qingwei/qingwei/infer_yolov5_v6_0.py

    cp -r ./retrieve/pyarmor_runtime_000000 ./retrieve/tools/

    mkdir ./retrieve/configs/
    mkdir ./retrieve/dataset/
    mkdir ./retrieve/dataset/background
    mkdir ./retrieve/models/
    
    cp ./configs/config.yaml ./retrieve/configs/
    cp -r ./models/init_model ./retrieve/models/
    cp -r ./models/detection_model ./retrieve/models/
    cp ./qingwei/Arial.ttf ./retrieve/qingwei/Arial.ttf
    cp ./qingwei/infer_yolov5_v6_0.py ./retrieve/qingwei/infer_yolov5_v6_0.py
    cp -r ./snpe ./retrieve/

    # #####################后期删除#########################
    # cp -r ./dataset/background/* ./retrieve/dataset/background/
    # cp -r ./dataset/ziwaiyumi ./retrieve/dataset/
    # cp ./temp.sh ./retrieve/
    # #####################后期删除#########################

    touch ./retrieve/version_log.txt
    echo "$1" > ./retrieve/version_log.txt
}

# 执行主程序
main "$1"