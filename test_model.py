import numpy as np
from multiprocessing import Array
from multiprocessing import Manager
from torch.utils.data import DataLoader, Dataset
import os
import cv2
import torch
import torch.nn as nn
import cv2
from pytorch.engine.engine import ENGINE
from pytorch.configs.config import update_config
import time
from tqdm import tqdm
from PIL import Image
try:
    from torch.amp import GradScaler, autocast
except:
    from torch.cuda.amp import GradScaler, autocast
    

def clip_weights(model, clip_value=1.0):
    for name, module in model.named_modules():
        if isinstance(module, nn.Conv2d):  # 检测DW卷积
            weight_median = float(torch.median(module.weight.data.detach().cpu()))
            if module.groups == module.in_channels:
                module.weight.data.clamp_(weight_median-0.2, weight_median+0.2)
            else:
                module.weight.data.clamp_(weight_median-clip_value, weight_median+clip_value)
        if isinstance(module, nn.BatchNorm2d):
            weight_median = float(torch.median(module.weight.data.detach().cpu()))
            module.weight.data.clamp_(weight_median-0.5, weight_median+0.5)
            if module.bias is not None:
                bias_median = float(torch.median(module.bias.data.detach().cpu()))
                module.bias.data.clamp_(bias_median-clip_value, bias_median+clip_value) 

if __name__ == "__main__":
    DATASET_DIR = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/haimiantai"  # 数据集根目录
    background_folder = "/home/<USER>/work/lyh_file/retrieve_cpu_speed/dataset/background"
    imgsz = [384, 4096]
    batch_size = 8
    train_size = 9000
    
    engine = ENGINE()
    __project__ = os.getcwd()
    update_config(engine, __project__, "haimiantai", "Generation_1", imgsz, 0,
                  0, 0, 0, f"{background_folder}/Generation_1")
    from pytorch.utils.general import LOGGER
    engine.LOGGER = LOGGER

    train_instance_path = engine.config['DataLoader']['Train']['dataset']['cls_label_path']
    class_names = engine.config['Global']['cls_name']
    
    # from speed.speed_test_mulproces import analyze_dataset_txt
    # instance_gallery = analyze_dataset_txt(train_instance_path)

    from pytorch.data.dataloader.create_dataloader import LoadImagesAndLabelsInstanceAUG
    dataset = LoadImagesAndLabelsInstanceAUG(train_instance_path, 
                                            class_names, 
                                            batch_size, 
                                            imgsz,
                                            augment=True,  # augmentation
                                            hyp=engine.config,  # hyperparameters
                                            single_cls=False,
                                            train_size = train_size,
                                            background_path = background_folder)
    
    ###### 必须用DataLoader ， 不能用InfiniteDataLoader ##############
    dataloader = DataLoader(dataset,
                            batch_size=8,
                            shuffle=True,
                            num_workers=8,  
                            sampler=None,
                            pin_memory=True,
                            collate_fn=LoadImagesAndLabelsInstanceAUG.collate_fn)


    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    from pytorch.arch.backbone.yolov5.yolo import Model
    num_classes = 11
    model = Model(engine.config, engine.LOGGER, ch=3, nc=num_classes)
    # model_weight_path = f"{engine.config['Global']['pretrained_model']}/{engine.config['Arch']['Backbone']['name']}.pth"
    # if os.path.exists(model_weight_path):
    #     pre_weights = torch.load(model_weight_path)
    #     anchor_grid = model._modules['model']._modules['24'].anchor_grid
    #     pre_dict = {k: v for k, v in pre_weights.items() if
    #                 k in model.state_dict() and v.shape == model.state_dict()[k].shape}
    #     missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
    #     model._modules['model']._modules['24'].anchor_grid = anchor_grid
    #     engine.LOGGER.info(f"len(pre_dict): {len(pre_dict.keys())}")
    #     engine.LOGGER.info(f"missing_keys: {missing_keys}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    from pytorch.arch.backbone.yolov5.yolo import Model
    from pytorch.loss.yolov5_loss import ComputeLoss
    from pytorch.optimizer.optimizer import build_optimizer
    num_classes = 11
    engine.model = Model(engine.config, engine.LOGGER, ch=3, nc=num_classes)
    engine.model = engine.model.to(device)
    criterion = ComputeLoss(engine.config, engine.model)
    build_optimizer(engine)

    height, width = imgsz
    dummy_input = torch.randn(batch_size, 3, height, width).to(device)
    dummy_target = torch.ones((10,6)).to(device)
    
    from pytorch.engine.engine import ModelEMA
    engine.ema = ModelEMA(engine.model)
    scaler = GradScaler()
    accumulate, last_opt_step = 8 , -1 
    for i in range(3):
        start = time.time()
        for j in tqdm(range(len(dataloader)), total=len(dataloader)):
            ni = j + len(dataloader) * i
            with autocast('cuda'):
                outputs = engine.model(dummy_input)
                loss, loss_item = criterion(outputs, dummy_target)
            scaler.scale(loss).backward()
            if ni - last_opt_step >= accumulate:
                scaler.step(engine.optimizer) 
                scaler.update()
                last_opt_step = ni 
                engine.optimizer.zero_grad()
                ###############20250424修改CLIP中值权重######################
                clip_weights(engine.model, clip_value=1.0)
                engine.ema.update(engine.model)
                clip_weights(engine.ema.ema, clip_value=1.0)
                ########################################################    
        engine.scheduler.step()
        print(f"Model computation time: {time.time() - start}s")