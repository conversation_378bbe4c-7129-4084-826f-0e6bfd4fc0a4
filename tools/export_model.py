import onnxsim
import onnx
import torch
import os
import shutil
import random
from tools.creat_raw_list import creat_raw_list
import onnx_graphsurgeon as gs
import sys
from pytorch.utils.general import install_package
import importlib

def get_model_anchors(model_name, best_model=None, train_log=None):
    """从yolov5_arch.py文件中获取模型的原始anchors定义"""
    if train_log:
        train_log.info(f"尝试从yolov5_arch.py获取{model_name}的原始anchors定义")
    
    try:
        # 直接导入backbone模块中的模型类
        module = importlib.import_module("pytorch.arch.backbone.yolov5.yolov5_arch")
        
        # 检查模块中是否有对应名称的类
        if hasattr(module, model_name):
            # 获取类并初始化
            model_class = getattr(module, model_name)
            model_instance = model_class()
            # 获取原始anchors定义
            if hasattr(model_instance, 'anchors'):
                anchors = model_instance.anchors
                if train_log:
                    train_log.info(f"成功从yolov5_arch.py获取{model_name}的anchors: {anchors}")
                return anchors
        
        # 如果找不到对应的类或属性，直接报错
        if train_log:
            train_log.info(f"错误: 在yolov5_arch.py中未找到{model_name}类或其anchors属性")
        return None
    
    except Exception as e:
        if train_log:
            train_log.info(f"错误: 获取anchors时发生异常: {str(e)}")
        return None

def check_or_create_docker_container(container_name, image_id, project_root, train_log, project_name="retrieve"):
    """尝试创建容器，如果已存在则直接启动"""
    # 构建映射路径，使用项目根目录
    mount_path = project_root
    
    # 首先检查容器是否存在
    check_cmd = f"sudo docker ps -a --format '{{{{.Names}}}}' | grep '^{container_name}$'"
    result = os.system(check_cmd + " > /dev/null 2>&1")
    
    if result == 0:
        # 容器存在，尝试启动
        train_log.info(f"容器 {container_name} 已存在，尝试启动")
        start_cmd = f"sudo docker start {container_name}"
        start_result = os.system(start_cmd)
        if start_result == 0:
            train_log.info(f"容器 {container_name} 启动成功")
            return True
        else:
            train_log.info(f"容器 {container_name} 启动失败")
            return False
    else:
        # 容器不存在，创建新容器
        train_log.info(f"容器 {container_name} 不存在，创建新容器")
        create_cmd = f"sudo docker run --name {container_name} -itd -v {mount_path}:/project {image_id} /bin/bash"
        create_result = os.system(create_cmd)
        if create_result == 0:
            train_log.info(f"容器 {container_name} 创建成功")
            return True
        else:
            train_log.info(f"容器 {container_name} 创建失败")
            return False

def export_model_with_gaotong_platform(best_model, config, project_root, model_dir, material_name,
                                       folder_name, platform, infer_batch, image_size, train_log):

    #### 转换onnx #####
    best_model.eval()
    if 'YOLOv5' in config['Arch']['Backbone']['name']:
        best_model.model[-1].export_model = True
    dummy_input = torch.randn(infer_batch, 3, image_size[0], image_size[1]).type(torch.FloatTensor).to('cpu')
    torch.onnx.export(best_model, dummy_input, f"{model_dir}/feature.onnx", opset_version=11)
    onnx_model = onnx.load(f"{model_dir}/feature.onnx")
    try:
        train_log.info(f'simplifying with onnx-simplifier {onnxsim.__version__}...')
        onnx_model, check = onnxsim.simplify(onnx_model)
        assert check, 'assert check failed'
        onnx.save(onnx_model, f"{model_dir}/feature.onnx")
    except Exception as e:
        train_log.info(f'onnx simplifier failure: {e}')

    #### 准备量化数据集 #####
    IMG_HEIGHT = image_size if isinstance(image_size, int) else image_size[0]
    IMG_WIDTH = image_size if isinstance(image_size,int) else image_size[1]
    creat_raw_list(config, material_name, folder_name, dir=model_dir, IMG_WIDTH=IMG_WIDTH, IMG_HEIGHT=IMG_HEIGHT)

    if sys.platform.startswith('linux'):
        ##### 环境变量配置 #####
        package_name = "libc++1"
        install_package(package_name)

        os.environ["LD_LIBRARY_PATH"] = f"{project_root}/snpe/lib/x86_64-linux-clang:{os.environ.get('LD_LIBRARY_PATH', '')}"
        command = f"chmod 777 {project_root}/snpe/*"
        os.system(command)
        
        #### onnx 转 dlc #####
        command = f"{project_root}/snpe/snpe-onnx-to-dlc --input_network {model_dir}/feature.onnx --output_path {model_dir}/feature_org.dlc"
        os.system(command)

        #### 量化 #####
        command = f"{project_root}/snpe/snpe-dlc-quant --input_dlc {model_dir}/feature_org.dlc --input_list {model_dir}/input.txt --output_dlc {model_dir}/feature_quant.dlc"
        os.system(command)

        # ##### 生成DSP离线缓存 #####
        graph = gs.import_onnx(onnx_model)
        output_tensors = ",".join([output.name for output in graph.outputs])
        command = f"{project_root}/snpe/snpe-dlc-graph-prepare --input_dlc {model_dir}/feature_quant.dlc --output_dlc {model_dir}/feature.dlc --htp_archs v68 --set_output_tensors {output_tensors}"
        os.system(command)




def export_model_with_qingwei_platform(best_model, config, project_root, model_dir, material_name,folder_name, platform, infer_batch, image_size, train_log):
    
    train_log.info(f"=== 开始处理模型: {material_name} ===")
    
    # 获取类别数量和图像尺寸
    class_num = len(config['Global']['cls_name'])
    IMG_HEIGHT = image_size if isinstance(image_size, int) else image_size[0]
    IMG_WIDTH = image_size if isinstance(image_size, int) else image_size[1]
    
    # 验证集路径
    val_path = config['Global']['val_path']
    train_log.info(f"模型信息: {material_name} ({IMG_HEIGHT}x{IMG_WIDTH}, {class_num} 类)")
    
    # 数据集路径
    material_data_dir = f"{project_root}/qingwei"
    os.makedirs(material_data_dir, exist_ok=True)
    
    # 1. 处理清微平台所需的YAML配置文件
    train_log.info("1. 创建YAML配置文件...")
    yaml_path = f"{material_data_dir}/data_config.yaml"
    rel_val_path = os.path.relpath(val_path, project_root)
    
    # 格式化类别名称
    class_names = []
    for idx, name in sorted([(int(v), k) for k, v in config['Global']['cls_name'].items()]):
        class_names.append(f"      {idx}: '{name}'")
    
    yaml_template = f"""
# YOLOv5 🚀 by Ultralytics, AGPL-3.0 license
# {material_name} dataset

# Train/val/test sets
val: /project/{rel_val_path}/Docker_val.txt  # val images (Docker路径)

# Classes
nc: {class_num}  # number of classes
names: # class names
{os.linesep.join(class_names)}
    """
    
    # 检查是否存在现有配置文件
    if os.path.exists(yaml_path):
        train_log.info(f"发现现有清微配置YAML: {yaml_path}，将进行更新")
        
        # 读取现有配置文件并更新内容
        import re
        with open(yaml_path, 'r') as f:
            existing_yaml = f.read()
        
        # 更新物料名称注释
        existing_yaml = re.sub(r'# .* dataset', f'# {material_name} dataset', existing_yaml)
        
        # 更新验证集路径
        existing_yaml = re.sub(r'val: .*', f'val: /project/{rel_val_path}/Docker_val.txt  # val images (Docker路径)', existing_yaml)
        
        # 更新类别数量
        existing_yaml = re.sub(r'nc: \d+', f'nc: {class_num}', existing_yaml)
        
        # 更新类别名称 
        names_pattern = r'names:.*?(?=\n\s*\n|\Z)'
        names_replacement = f'names: # class names\n{os.linesep.join(class_names)}'
        existing_yaml = re.sub(names_pattern, names_replacement, existing_yaml, flags=re.DOTALL)
        
        # 写回更新后的内容
        with open(yaml_path, 'w') as f:
            f.write(existing_yaml)
    else:
        # 如果不存在，则创建新文件
        with open(yaml_path, 'w') as f:
            f.write(yaml_template)
    
    train_log.info("√ YAML配置文件创建完成")
    
    # 2. 准备验证集数据 - 使用val.txt中的路径并转换为Docker路径
    train_log.info("2. 准备验证集数据...")
    val_txt_path = f"{val_path}/val.txt"
    docker_val_txt_path = f"{val_path}/Docker_val.txt"
    
    if os.path.exists(val_txt_path):
        # 读取原始val.txt
        with open(val_txt_path, 'r') as f:
            val_lines = f.readlines()
        
        # 随机选择300行或全部(如果少于300行)
        selected_lines = val_lines
        if len(val_lines) > 300:
            selected_lines = random.sample(val_lines, 300)
        
        # 将路径转换为Docker内路径并写入新文件
        with open(docker_val_txt_path, 'w') as f:
            for line in selected_lines:
                # 转换路径从主机路径到Docker路径
                docker_path = line.strip().replace(project_root, "/project")
                f.write(f"{docker_path}\n")
        
        train_log.info(f"√ 验证集准备完成，包含 {len(selected_lines)} 张图片")
    else:
        train_log.info(f"错误: 验证集文件不存在: {val_txt_path}")
        return
    
    # 3. 修改清微模型推理配置文件
    train_log.info("3. 更新推理配置文件...")
    infer_py_path = f"{project_root}/qingwei/infer_yolov5_v6_0.py"
    if os.path.exists(infer_py_path):
        with open(infer_py_path, 'r') as f:
            infer_py_content = f.read()
        
        # 获取模型名称和对应的anchors
        model_name = config['Arch']['Backbone']['name']
        model_anchors = get_model_anchors(model_name, best_model, train_log)
        
        # 检查是否成功获取anchors
        if model_anchors is None:
            train_log.info(f"错误: 无法获取模型 {model_name} 的anchors，导出中止")
            return
        
        # 生成anchors字符串
        anchors_str = "[\n"
        if len(model_anchors) == 1:
            anchors_str += f"  # 1 anchor for {model_name}\n  {str(model_anchors[0])}  # P1/16\n"
        elif len(model_anchors) == 2:
            anchors_str += f"  # 2 anchors for {model_name}\n  {str(model_anchors[0])},  # P2/4\n  {str(model_anchors[1])}  # P3/8\n"
        else:  # 3 anchors
            anchors_str += f"  # 3 anchors for {model_name}\n  {str(model_anchors[0])},  # P3/8\n  {str(model_anchors[1])},  # P4/16\n  {str(model_anchors[2])}  # P5/32\n"
        anchors_str += "]"
        
        # 修改配置模板 - 使用固定YAML文件名
        config_template = f"""
# 根据物料修改如下参数
config_yolov5s={{
'nc': {class_num},  # number of classes
'imgsz' :[{IMG_HEIGHT}, {IMG_WIDTH}], #  h,w
'iou':0.45,
'conf':0.25,
'dataPath':'/project/qingwei/data_config.yaml',# docker
# 根据使用的模型自动选择对应的anchors值 - {model_name}
'anchors':{anchors_str}
}}

"""
        # 替换配置部分
        import re
        infer_py_content = re.sub(r'# 根据物料修改如下参数\nconfig_yolov5s=\{[^}]+\}', config_template.strip(), infer_py_content)
        
        # 修改模型注册装饰器
        register_pattern = r'@pytorch_model\.register\("[^"]+"\)'
        infer_py_content = re.sub(register_pattern, f'@pytorch_model.register("{material_name}")', infer_py_content)
        
        # 写回修改后的文件
        with open(infer_py_path, 'w') as f:
            f.write(infer_py_content)
        
        train_log.info("√ 推理配置文件更新完成")
    else:
        train_log.info(f"错误: 清微推理文件不存在: {infer_py_path}")
        return
    
    # 4. 检查或创建Docker容器
    train_log.info("4. 检查Docker容器...")
    # 映射路径设置
    host_project_dir = project_root
    docker_project_dir = "/project"
    
    # Docker内路径 - 使用原始模型所在目录
    rel_model_dir = os.path.relpath(model_dir, project_root)
    docker_model_dir = f"{docker_project_dir}/{rel_model_dir}"
    
    # 使用固定容器名称
    container_name = "qingwei"
    # 清微Docker镜像ID
    docker_image_id = "2fb060205791"
    
    # 清微平台量化迭代次数参数
    knight_quantize_iter = 10
    
    # 检查或创建容器，传入自定义项目名称
    container_created = check_or_create_docker_container(container_name, docker_image_id, project_root, train_log, project_name="retrieve")
    if container_created:
        train_log.info("√ Docker容器准备完成")
    else:
        train_log.info("错误: Docker容器准备失败")
        return

    # 获取模型anchors数量，用于老版本清微平台判断
    anchors_count = len(model_anchors)
    
    # 5. 执行量化和编译
    train_log.info("5. 执行模型量化和编译...")
    
    # 根据platform参数选择不同的清微量化命令
    if int(platform) == 2:  # 新版本清微量化
        train_log.info(f"使用新版TX5336AV200清微量化流程 (platform=2)")
        docker_cmd = f"""
        sudo docker exec -i {container_name} /bin/bash -c "
        export PYTHONPATH=/project:$PYTHONPATH
        cd /project &&
        echo '开始执行Knight量化命令' && 
        Knight --chip TX5336AV200 quant onnx -m {material_name} -f pytorch -if infer_yolov5_v6_0 --save-dir {docker_model_dir} -qm mse -w {docker_model_dir}/best_model.pt -uds /project/qingwei/infer_yolov5_v6_0.py -i {knight_quantize_iter} -uis && 
        echo '开始执行Knight编译命令' && 
        Knight --chip TX5336AV200 compile --onnx {docker_model_dir}/{material_name}_quantize.onnx --save-dir {docker_model_dir} --hardware-resource-mode super --rgb && 
        echo '清微模型量化完成' &&
        echo '清理多余文件，只保留.cfg和.weight文件，保留RecModel文件夹' &&
        cd {docker_model_dir} &&
        echo '重命名.cfg和.weight文件' &&
        mv {material_name}_quantize_r.weight feature.weight &&
        mv {material_name}_quantize_r.cfg feature.cfg &&
        find . -type f -not -path './RecModel/*' -not -name '*.weight' -not -name '*.cfg' -delete &&
        rm -rf log steps &&
        echo '文件清理完成'
        "
        """
    else:  # 老版本清微量化 (platform=1)
        train_log.info(f"使用老版TX5368AV200清微量化流程 (platform=1)")
        
        # 老版本基于anchors数量决定是否需要剪裁
        if anchors_count == 2:
            train_log.info(f"模型有2个anchors，将进行剪裁")
            docker_cmd = f"""
            sudo docker exec -i {container_name} /bin/bash -c "
            # 设置 PYTHONPATH, 同时加入清微量化工具路径（/usr/local/core/quantize）
            export PYTHONPATH=/project:/usr/local/core/quantize:$PYTHONPATH
            cd /project &&
            echo '开始执行Knight量化命令(剪裁模式)' && 
            Knight --chip TX5368AV200 quant onnx -m {material_name} -f pytorch -if infer_yolov5_v6_0 --save-dir {docker_model_dir} -qm mse -w {docker_model_dir}/best_model.pt -uds /project/qingwei/infer_yolov5_v6_0.py -i {knight_quantize_iter} -uis && 
            echo '执行模型裁剪' &&
            # 复制裁剪脚本到量化工具目录
            cp /project/qingwei/cut_subgraph.py /usr/local/core/quantize/onnx_quantize_tool/tools/ && \
            python /usr/local/core/quantize/onnx_quantize_tool/tools/cut_subgraph.py -m {docker_model_dir}/{material_name}_quantize.onnx -on /model.13/m.0/Conv_output_0 /model.13/m.1/Conv_output_0 -s {docker_model_dir} &&
            echo '开始执行Knight编译命令' && 
            Knight --chip TX5368AV200 compile --onnx {docker_model_dir}/subgraph.onnx --save-dir {docker_model_dir} && 
            echo '清微模型量化完成' &&
            echo '清理多余文件，只保留.cfg和.weight文件，保留RecModel文件夹' &&
            cd {docker_model_dir} &&
            echo '重命名.cfg和.weight文件' &&
            mv subgraph_r.weight feature.weight &&
            mv subgraph_r.cfg feature.cfg &&
            find . -type f -not -path './RecModel/*' -not -name '*.weight' -not -name '*.cfg' -delete &&
            rm -rf log steps &&
            echo '文件清理完成'
            "
            """
        else:  # 3个anchors，不需要剪裁
            train_log.info(f"模型有3个anchors，不进行剪裁")
            docker_cmd = f"""
            sudo docker exec -i {container_name} /bin/bash -c "
            export PYTHONPATH=/project:$PYTHONPATH
            cd /project &&
            echo '开始执行Knight量化命令' && 
            Knight --chip TX5368AV200 quant onnx -m {material_name} -f pytorch -if infer_yolov5_v6_0 --save-dir {docker_model_dir} -qm mse -w {docker_model_dir}/best_model.pt -uds /project/qingwei/infer_yolov5_v6_0.py -i {knight_quantize_iter} -uis && 
            echo '开始执行Knight编译命令' && 
            Knight --chip TX5368AV200 compile --onnx {docker_model_dir}/{material_name}_quantize.onnx --save-dir {docker_model_dir} && 
            echo '清微模型量化完成' &&
            echo '清理多余文件，只保留.cfg和.weight文件，保留RecModel文件夹' &&
            cd {docker_model_dir} &&
            echo '重命名.cfg和.weight文件' &&
            mv {material_name}_quantize_r.weight feature.weight &&
            mv {material_name}_quantize_r.cfg feature.cfg &&
            find . -type f -not -path './RecModel/*' -not -name '*.weight' -not -name '*.cfg' -delete &&
            rm -rf log steps &&
            echo '文件清理完成'
            "
            """
    
    result = os.system(docker_cmd)
    
    if result == 0:
        train_log.info("√ 模型量化和编译成功")
        # 检查生成的文件
        weight_file = f"{model_dir}/feature.weight"
        cfg_file = f"{model_dir}/feature.cfg"
        if os.path.exists(weight_file) and os.path.exists(cfg_file):
            train_log.info(f"已生成模型文件: {weight_file} 和 {cfg_file}")
        else:
            train_log.info(f"警告: 模型文件未找到: {weight_file} 或 {cfg_file}")
    else:
        train_log.info(f"错误: 模型量化和编译失败，错误码: {result}")
        return
    
    # 6. 修改文件权限
    train_log.info("6. 修改文件权限...")
    chown_cmd = f"sudo chown -R $(id -u):$(id -g) {model_dir}"
    os.system(chown_cmd)
    train_log.info("√ 文件权限修改完成")
        
    # 7. 将anchors写入cfg文件
    train_log.info("7. 将anchors写入cfg文件...")
    try:
        import struct
        import json
        import tempfile
        
        def convert_anchors_format(anchors):
            """将不同格式的anchors转换为cfg文件所需的统一格式"""
            # 如果是单层anchors列表，直接处理成二维列表表示
            if isinstance(anchors[0], (int, float)):
                # 假设是平铺的格式 [w1,h1, w2,h2, ...]
                anchor_pairs = []
                for i in range(0, len(anchors), 2):
                    if i+1 < len(anchors):  # 确保有足够的元素
                        anchor_pairs.append([float(anchors[i]), float(anchors[i+1])])
                return [anchor_pairs]
            
            # 如果是多层anchors列表
            converted = []
            for layer in anchors:
                # 如果层本身是平铺的 [w1,h1, w2,h2, ...]
                if isinstance(layer[0], (int, float)):
                    pairs = []
                    for i in range(0, len(layer), 2):
                        if i+1 < len(layer):  # 确保有足够的元素
                            pairs.append([float(layer[i]), float(layer[i+1])])
                    converted.append(pairs)
                # 如果层已经是[[w1,h1], [w2,h2], ...]格式
                elif isinstance(layer[0], (list, tuple)):
                    converted.append([[float(w), float(h)] for w, h in layer])
            
            return converted

        def write_anchors_to_cfg(cfg_path, anchors):
            """将anchors写入到cfg文件"""
            anchors = convert_anchors_format(anchors)
            anchors_tag_start = b'ANCHORS_START'
            anchors_tag_end = b'ANCHORS_END'
            anchors_bytes = b''
            anchors_bytes += struct.pack('<i', len(anchors))
            for layer in anchors:
                anchors_bytes += struct.pack('<i', len(layer))
                for pair in layer:
                    anchors_bytes += struct.pack('<f', float(pair[0]))
                    anchors_bytes += struct.pack('<f', float(pair[1]))
            try:
                with open(cfg_path, 'rb') as f:
                    content = f.read()
                if anchors_tag_start in content and anchors_tag_end in content:
                    start = content.find(anchors_tag_start)
                    end = content.find(anchors_tag_end) + len(anchors_tag_end)
                    content = content[:start] + content[end:]
                    with open(cfg_path, 'wb') as f:
                        f.write(content)
                with open(cfg_path, 'ab') as f:
                    f.write(anchors_tag_start)
                    f.write(anchors_bytes)
                    f.write(anchors_tag_end)
                return True
            except Exception as e:
                if 'train_log' in globals():
                    train_log.info(f"写入anchors时发生错误: {str(e)}")
                return False

        # 准备anchors数据
        
        # 处理cfg文件路径
        cfg_file_path = f"{model_dir}/feature.cfg"
        
        # 调用函数写入anchors
        if write_anchors_to_cfg(cfg_file_path, model_anchors):
            train_log.info("√ anchors成功写入到cfg文件")
        else:
            train_log.info("警告: anchors写入失败")
            
    except Exception as e:
        train_log.info(f"错误: 处理anchors时出错: {str(e)}")
    
    train_log.info(f"=== 模型 {material_name} 处理完成 ===\n")


def export_model(best_model, config, project_root, model_dir, material_name,
                 folder_name, platform, infer_batch, image_size, train_log):
    if config['Global']['platform']=='gaotong':
        export_model_with_gaotong_platform(best_model, config, project_root, model_dir, material_name,
                                           folder_name, platform, infer_batch, image_size, train_log)
    elif config['Global']['platform']=='qingwei':
        export_model_with_qingwei_platform(best_model, config, project_root, model_dir, material_name,
                                           folder_name, platform, infer_batch, image_size, train_log)
    elif config['Global']['platform']=='Nvidia':
        pass
    return

