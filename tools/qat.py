import torch
import torchvision
from torch.utils.data import DataLoader
from tqdm import tqdm
from aimet_torch.batch_norm_fold import fold_all_batch_norms
from aimet_common.defs import QuantScheme
from aimet_torch.quantsim import QuantizationSimModel


PATH_TO_IMAGENET = "/home/<USER>/work/lyh_file/retrieve/dataset/peanut_eval_data_file"
model_weight_path = f"/home/<USER>/work/lyh_file/retrieve/models/huashengmi/Generation_2/best_model.pth"
CLS_NUM = 8
################################################################

### General setup that can be changed as needed
device = "cuda:0" if torch.cuda.is_available() else "cpu"

# model = torchvision.models.mobilenet_v2(pretrained=True).eval().to(device)
model = timm.create_model('mobilenetv4_conv_small', pretrained=False, num_classes=1000)  # 使用动态获取的类别数
model.conv_head = nn.Conv2d(960, 128, kernel_size=1, stride=1, padding=0, bias=False)
model.norm_head = nn.BatchNorm2d(128)
model.classifier = nn.Linear(128, CLS_NUM)   # 替换最后的分类层，使其适应你的类别数
### pre weights 
if os.path.exists(model_weight_path):
    pre_weights = torch.load(model_weight_path)
    pre_dict = {k: v for k, v in pre_weights.items() if
                k in model.state_dict() and v.shape == model.state_dict()[k].shape}
    missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
    engine.LOGGER.info(f"len(pre_dict): {len(pre_dict.keys())}")
    engine.LOGGER.info(f"missing_keys: {missing_keys}")


batch_size = 64
# data = torchvision.datasets.ImageNet(PATH_TO_IMAGENET, split="train")
data = torchvision.datasets.ImageNet(PATH_TO_IMAGENET)
data_loader = DataLoader(data, batch_size=batch_size)

dummy_input = torch.randn(1, 3, 128, 128).to(device)
fold_all_batch_norms(model, dummy_input.shape)

### Callback function to pass calibration data through the model
def pass_calibration_data(model: torch.nn.Module, batches):
    """
    The User of the QuantizationSimModel API is expected to write this callback based on their dataset.
    """
    with torch.no_grad():
        for batch, (images, _) in enumerate(data_loader):
            images = images.to(device)
            model(images)
            if batch >= batches:
                break

### Basic ImageNet evaluation function
def evaluate(model, data_loader):
    model.eval()
    correct = 0
    with torch.no_grad():
        for data, labels in tqdm(data_loader):
            data, labels = data.to(device), labels.to(device)
            logits = model(data)
            correct += (logits.argmax(1) == labels).type(torch.float).sum().item()
    accuracy = correct / len(data_loader.dataset)
    return accuracy


sim = QuantizationSimModel(model, dummy_input, quant_scheme=QuantScheme.training_range_learning_with_tf_init)

calibration_batches = 10
sim.compute_encodings(pass_calibration_data, calibration_batches)

accuracy = evaluate(sim.model, data_loader)
print(f"Quantized accuracy (W8A8): {accuracy}")

def train(model, data_loader, optimizer, loss_fn):
    model.train()
    for data, labels in tqdm(data_loader):
        data, labels = data.to(device), labels.to(device)
        logits = model(data)
        loss = loss_fn(logits, labels)
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()

loss_fn = torch.nn.CrossEntropyLoss()
optimizer = torch.optim.SGD(sim.model.parameters(), lr=1e-5)

epochs = 2
for epoch in range(epochs):
    train(sim.model, data_loader, optimizer, loss_fn)

accuracy = evaluate(sim.model, data_loader)
print(f"Model accuracy after QAT: {accuracy}")

sim.export(path=os.path.dirname(model_weight_path), filename_prefix="quantized_mobilenetv4", dummy_input=dummy_input.cpu())