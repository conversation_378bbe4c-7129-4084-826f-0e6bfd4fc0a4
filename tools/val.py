
import onnxruntime
import cv2
import numpy as np
from tqdm import tqdm
import os
import json
from torchvision import  models
import torch.nn as nn
import torch
from pytorch.engine.CustomOps import chang_model_act

model_weight_path = r"E:\jiemi\Classification\retrieve_pytorch\models\mt1\Generation_2/best_model.pth"
imgPathTxt = r"E:\jiemi\Classification\retrieve_pytorch\dataset\mt1/eval_data_file.txt"
with open(f"{os.path.dirname(imgPathTxt)}/subfolder_to_id.json", 'r') as file:
    class_to_idx = json.load(file)

model = models.mobilenet_v3_large(weights=None, width_mult=0.35)
num_ftrs = model.classifier[3].in_features
model.classifier[3] = nn.Linear(num_ftrs, len(class_to_idx))

model = chang_model_act(model)

pre_weights = torch.load(model_weight_path)
pre_dict = {k: v for k, v in pre_weights.items() if
            k in model.state_dict() and v.shape == model.state_dict()[k].shape}
missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=True)
print(f"len(pre_dict): {len(pre_dict.keys())}")
print(f"missing_keys: {missing_keys}")


with open(imgPathTxt, "r") as fc:
    lines = fc.read().strip().splitlines()

Batch = 24
total = 0.0
acc = 0.0
input_tensor = []
label_List = []
model.eval()
with torch.no_grad():
    for line in tqdm(lines):
        imgPath, label, _ = line.split(" ")
        image = cv2.imread(imgPath)

        image = image[:, :, ::-1]
        img_h, img_w = image.shape[:2]
        percent = min(float(128 / img_w), float(128 / img_h))
        w = int(round(img_w * percent))
        h = int(round(img_h * percent))
        image = cv2.resize(image, (w, h))
        offset_y, offset_x = (128 - h) // 2, (128 - w) // 2
        canvas = np.zeros((128, 128, 3), dtype=image.dtype)
        canvas[offset_y:offset_y + h, offset_x:offset_x + w, :] = image
        image = canvas.astype('float32') * 1/255

        image = np.transpose(image, (2, 0, 1)).astype(np.float32)
        image = np.expand_dims(image, axis = 0)
        input_tensor.append(image)
        label_List.append(int(class_to_idx[label]))

        if len(input_tensor)==Batch:
            input_tensor = np.vstack(input_tensor)
            input_tensor = torch.from_numpy(np.stack(input_tensor))
            output = model(input_tensor)
            predicted_class = torch.argmax(output, axis=1)
            label_List = torch.from_numpy(np.stack(label_List))
            right = predicted_class == label_List
            acc += float(torch.sum(right))
            total += Batch
            input_tensor = []
            label_List = []
acc = float(acc/total)
print("acc:",acc)