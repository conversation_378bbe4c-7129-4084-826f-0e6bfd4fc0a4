
import os
import sys
from pathlib import Path
import numpy as np
import cv2
import torch
import torch.backends.cudnn as cudnn
import yaml

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative

from pytorch.arch.backbone.yolov5.common import DetectMultiBackend
from pytorch.utils.general import IMG_FORMATS, LoadImages
from pytorch.utils.general import (LOGGER, check_file, check_img_size, check_imshow, check_requirements, colorstr,
                           increment_path, non_max_suppression, print_args, scale_coords, xyxy2xywh)
from pytorch.utils.plots import Annotator, colors, save_one_box


model_weight_path = r"E:\jiemi\Classification\retrieve\models\haimiantai\Generation_1/best_model.pt"
imgPath = r"E:\jiemi\Classification\retrieve\dataset\haimiantai_pre"
half = False
imgsz = [384, 4096]
conf_thres = 0.1
iou_thres = 0.5
classes_Name = ["label1","label2","label3","label4","label5","label6",
                "label7","label8","label9","label10","label11"]
agnostic_nms = False

if __name__ == "__main__":
    source = str(imgPath)
    save_img = True
    is_file = Path(source).suffix[1:] in IMG_FORMATS

    # Directories
    save_dir = Path(f"{os.path.dirname(imgPath)}/{os.path.basename(imgPath)}_detect")

    # Load model
    device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
    model = DetectMultiBackend(model_weight_path, device=device)

    model.model.model[-1].export = False

    stride, names, pt, jit, onnx, engine = model.stride, classes_Name, model.pt, model.jit, model.onnx, model.engine
    imgsz = check_img_size(imgsz, s=stride)  # check image size

    # Half

    half &= (pt or engine) and device.type != 'cpu'  # half precision only supported by PyTorch on CUDA
    model.model.half() if (half and pt) else model.model.float()

    dataset = LoadImages(source, img_size=imgsz, stride=stride, auto=pt and not jit)
    bs = 1  # batch_size
    vid_path, vid_writer = [None] * bs, [None] * bs

    # Run inference
    # model.warmup(imgsz=(1, 3, *imgsz), half=half)  # warmup
    dt, seen = [0.0, 0.0, 0.0], 0
    for i, (path, im, im0s, s) in enumerate(dataset):
        im = torch.from_numpy(im).to(device)
        im = im.half() if half else im.float()  # uint8 to fp16/32
        im /= 255  # 0 - 255 to 0.0 - 1.0
        if len(im.shape) == 3:
            im = im[None]  # expand for batch dim

        # Inference
        pred = model(im, augment=False)
        # NMS
        pred = non_max_suppression(pred, conf_thres=conf_thres, iou_thres=iou_thres, agnostic=agnostic_nms)

        # Process predictions
        for i, det in enumerate(pred):  # per image
            # temp_save = 0
            seen += 1
            p, im0, frame = path, im0s.copy(), getattr(dataset, 'frame', 0)

            im0s = im0.copy()
            p = Path(p)  # to Path
            save_path = str(save_dir / p.name)  # im.jpg
            s += '%gx%g ' % im.shape[2:]  # print string
            gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
            imc = im0.copy()
            annotator = Annotator(im0, line_width=2, example=str(names))
            if len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = scale_coords(im.shape[2:], det[:, :4], im0.shape).round()
                # for d in det:
                #    if d[5]==1 and d[4]>0.9:
                #        temp_save = 1

                # Print results
                for c in det[:, -1].unique():
                    n = (det[:, -1] == c).sum()  # detections per class
                    s += f"{n} {names[int(c)]}{'s' * (n > 1)}, "  # add to string

                # Write results
                for *xyxy, conf, cls in reversed(det):
                    c = int(cls)  # integer class
                    label = f'{names[c]} {conf:.2f}'
                    annotator.box_label(xyxy, label, color=colors(c, True))
                    save_one_box(xyxy, imc, file=save_dir / 'crops' / names[c] / f'{conf}.jpg' , BGR=True)

            im0 = annotator.result()
            # if temp_save:
            # im_join = np.zeros((im0s.shape[0]*2, im0s.shape[1], 3), dtype=np.uint8)
            # im_join[0:im0s.shape[0], :, :] = im0s
            # im_join[im0s.shape[0]:im0s.shape[0]*2, :, :] = im0
            # cv2.imwrite(save_path, im_join)
            cv2.imwrite(save_path, im0)

    # Print results
    t = tuple(x / seen * 1E3 for x in dt)  # speeds per image
    LOGGER.info(f'Speed: %.1fms pre-process, %.1fms inference, %.1fms NMS per image at shape {(1, 3, *imgsz)}' % t)