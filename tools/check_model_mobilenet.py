import os

import torch
import numpy as np
import pandas as pd
import uuid
from torchvision import models
import torch.nn as nn
from pytorch.engine.CustomOps import chang_model_act

# 模型路径
base_paths = r"E:\jiemi\Classification\retrieve\models\Seed_384_2K_CLS_1000"
num_cls = 17
model = models.mobilenet_v3_large(pretrained=False, width_mult=0.35)
num_ftrs = model.classifier[3].in_features
model.classifier[3] = nn.Linear(num_ftrs, num_cls)
model = chang_model_act(model)
model_paths = {"Generation_2":"E:/jiemi/Classification/retrieve/models/Seed_384_2K_CLS_1000/Generation_2"}
savePathbase="E:/jiemi/Classification/retrieve/models/Seed_384_2K_CLS_1000"

results = {}
for model_name, model_path in model_paths.items():
    if os.path.exists(f"{model_path}/best_model.pth"):
        pre_weights = torch.load(f"{model_path}/best_model.pth")
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in model.state_dict() and v.shape == model.state_dict()[k].shape}
        missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
        print(f"len(pre_dict): {len(pre_dict.keys())}")
        print(f"missing_keys: {missing_keys}")
    model.eval()
    layer_diffs = {}
    for param_name, param in model.named_parameters():
        module_name = param_name.rsplit('.', 1)[0]  # 例如：features.block.2.conv.weight -> features.block.2.conv
        try:
            module = model.get_submodule(module_name)
            weights = module.weight.detach().cpu().numpy()
            diff = np.max(weights) - np.min(weights)
            if isinstance(module, torch.nn.Conv2d):    # 仅处理卷积层（可选）
                if module.in_channels==module.groups:
                    param_name = param_name+"_DWConv"
                else:
                    param_name = param_name + "_Conv"
            layer_diffs[param_name] = diff
        except KeyError:
            print(f"Module '{module_name}' not found for parameter '{param_name}'")

    results[model_name] = layer_diffs

# 创建 DataFrame
layer_names = sorted(next(iter(results.values())).keys())
df = pd.DataFrame(index=layer_names, columns=model_paths.keys())
for model_name in results:
    for layer_name in layer_names:
        df.loc[layer_name, model_name] = results[model_name].get(layer_name, np.nan)

savePath = f"{savePathbase}/weight_diffs.csv"
df.to_csv(savePath)
print(f"CSV file {savePath} has been generated.")