import cv2
import numpy as np
import argparse
import os
import random
import platform
import shutil
import sys
if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    __dir__ = os.path.dirname(sys.executable)
    __project__ = os.path.abspath(os.path.join(__dir__, './'))
else:
    # 如果是脚本文件
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    __project__ = os.path.abspath(os.path.join(__dir__, '../'))
sys.path.append(__project__)
from pytorch.data.preprocess.instance_paste import paste_instance


def preprocess(raw_image, input_w, input_h):
    image = raw_image.copy()
    image_h, image_w, _ = image.shape

    # Resize
    scale = min(float(input_w) / image_w, float(input_h) / image_h)
    new_w = int(round(scale * image_w))
    new_h = int(round(scale * image_h))
    img = cv2.resize(image, dsize=(new_w, new_h), interpolation=cv2.INTER_LINEAR)
    # Compute the top-left corner for the image to be placed in the center
    top = (input_h - new_h) // 2
    left = (input_w - new_w) // 2
    # Pad
    pad_img = np.zeros((input_h, input_w, 3), dtype=np.float32)
    # pad_img = np.zeros((input_h, input_w, 3), dtype=np.int8)
    pad_img[top:top+new_h, left:left+new_w, :] = img
    img = pad_img
    # BGR to RGB
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = img/255.0
    return np.transpose(img, (0, 1, 2))[None , : ]

def creat_raw_list(config, material_name, model_name, dir=None, IMG_WIDTH=128, IMG_HEIGHT=128):
    quantFolder = f"{dir}/quantFolder"
    if not os.path.exists(quantFolder):
        os.mkdir(quantFolder)
    else:
        if platform.system() == 'Windows':
            shutil.rmtree(quantFolder)
            os.mkdir(quantFolder)
        elif platform.system() == 'Linux':
            command = f"rm -rf {quantFolder}/*"
            os.system(command)
    imgsPath = []
    if config["Global"]["update_mode"]:
        dataset_root_dir = os.path.join(__project__, f'dataset/{material_name}')
        data_file_path = os.path.join(dataset_root_dir, "data_file.txt")
        imgs_dict = {}
        with open(data_file_path) as file:
            lines = file.read().strip().splitlines()
        for line in lines:
            path, label, _ = line.split(" ")
            if label not in imgs_dict.keys():
                imgs_dict[label] = []
            else:
                imgs_dict[label].append(path)
        for key, value in imgs_dict.items():
            imgsPath += random.sample(value, k=min(20,len(value)))
        random.shuffle(imgsPath)
    else:
        val_path = f"{config['Global']['val_path']}/val.txt"
        with open(val_path) as f:
            lines = f.read().strip().splitlines()
            imgsPath = random.sample(lines, k=min(20, len(lines)))
    _count = 0
    current_path = os.getcwd()
    raw_list_path = f"{dir}/input.txt"
    fp = open(raw_list_path, "w")
    for path in imgsPath:
        QuantImg = cv2.imread(path)
        QuantImg = preprocess(QuantImg, input_w=IMG_WIDTH, input_h=IMG_HEIGHT)
        file_name = quantFolder +"/"+ str(_count) + "_{}x{}".format(IMG_HEIGHT, IMG_WIDTH) + ".raw"
        _count += 1
        QuantImg.tofile(file_name)
        # fp.write(current_path+"/"+file_name+"\n")
        fp.write(file_name + "\n")
