import os
from pytorch.arch.backbone.yolov5.yolo import Model
import torch
import numpy as np
import pandas as pd
import uuid
from torchvision import models
import torch.nn as nn
from pytorch.engine.CustomOps import chang_model_act
from pytorch.engine.engine import ENGINE
from pytorch.configs.config import update_config
from pytorch.utils.general import LOGGER
import sys

# 模型路径
__project__ = os.path.dirname(os.getcwd())
base_paths = r"E:\jiemi\Classification\retrieve\models\haimiantai"
engine = ENGINE()
update_config(engine, __project__, "haimiantai", "Generation_1", [ 384, 4096 ], 0,
              0, 0, 0, f"{base_paths}/Generation_1")
model = Model(engine.config, LOGGER, ch=3, nc=11).model
model_paths = {"Generation_1":"E:/jiemi/Classification/retrieve/models/haimiantai/Generation_1"}
savePathbase="E:/jiemi/Classification/retrieve/models/haimiantai"

results = {}
for model_name, model_path in model_paths.items():
    if os.path.exists(f"{model_path}/best_model.pt"):
        pre_weights = torch.load(f"{model_path}/best_model.pt")['ema']
        missing_keys, unexpected_keys = model.load_state_dict(pre_weights.model.state_dict(), strict=False)
        print(f"missing_keys: {missing_keys}")
    model.eval()
    layer_diffs = {}
    for module_name, module in model.named_modules():
        try:
            if isinstance(module, torch.nn.Conv2d):    # 仅处理卷积层（可选）
                weights = module.weight.detach().cpu().numpy()
                diff = np.max(weights) - np.min(weights)
                if module.in_channels==module.groups:
                    param_name = module_name+"_DWConv"
                else:
                    param_name = module_name + "_Conv"
                layer_diffs[param_name] = diff
            if isinstance(module, torch.nn.BatchNorm2d):
                weights = module.weight.detach().cpu().numpy()
                diff = np.max(weights) - np.min(weights)
                param_name = module_name + "_BN_weights"
                layer_diffs[param_name] = diff
                bias = module.bias.detach().cpu().numpy()
                diff_bias = np.max(bias) - np.min(bias)
                param_name = module_name + "_BN_bias"
                layer_diffs[param_name] = diff_bias
        except KeyError:
            print(f"Module '{module_name}' not found for parameter '{module_name}'")

    results[model_name] = layer_diffs

# 创建 DataFrame
layer_names = sorted(next(iter(results.values())).keys())
df = pd.DataFrame(index=layer_names, columns=model_paths.keys())
for model_name in results:
    for layer_name in layer_names:
        df.loc[layer_name, model_name] = results[model_name].get(layer_name, np.nan)

savePath = f"{savePathbase}/weight_diffs.csv"
df.to_csv(savePath)
print(f"CSV file {savePath} has been generated.")