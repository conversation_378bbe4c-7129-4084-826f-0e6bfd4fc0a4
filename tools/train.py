import os
import sys
from pytorch.utils.general import LOGGER
import logging
from pytorch import *

if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    __dir__ = os.path.dirname(sys.executable)
    __project__ = os.path.abspath(os.path.join(__dir__, './'))
else:
    # 如果是脚本文件
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    __project__ = os.path.abspath(os.path.join(__dir__, '../'))
sys.path.append(__project__)

RANK = int(os.getenv('RANK', -1))

def train(material_name, folder_name, update_mode, image_size, platform,
          retain_size_info, model_dir, detection_model_id):

    ###################### 定义Engine #############################
    engine = ENGINE()

    ###################### 将log日志输出到指定路径 ######################
    if RANK in [-1, 0]:
        file_handler = logging.FileHandler(f"{__project__}/models/{material_name}/{folder_name}/RecModel/train.log", mode='a')  # 创建 FileHandler
        LOGGER.addHandler(file_handler)
    engine.LOGGER = LOGGER

    ###################### 生成并更新config：->dict ######################
    update_config(engine, __project__, material_name, folder_name, image_size, platform,
                  retain_size_info, update_mode, detection_model_id, model_dir)

    ######################### 使用预训练模型测试物料区分难易程度 ######################
    # engine.state = save_rng_state()
    # check_score(engine)
    # restore_rng_state(engine)

    ####################### 创建模型 ######################
    build_model(engine)
    engine.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    engine.model = engine.model.to(engine.device)

    ###################### 加载训练集和验证集 ######################
    build_dataLoader(engine)

    ###################### 定义损失函数和优化器 #####################
    build_loss(engine)
    build_optimizer(engine)

    ###################### 定义 metric #####################
    build_metric(engine)

    ###################### 开始训练模型 ########################
    engine.train()

    ###################### 加载best_model权重 ########################
    engine.update_weight()

    return engine.LOGGER, engine.model.to(torch.device("cpu")), engine.config



