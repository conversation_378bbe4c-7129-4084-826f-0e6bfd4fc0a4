import glob
import shutil

import onnxruntime
import cv2
import numpy as np
from tqdm import tqdm
import os
import json
from torchvision import  models
import torch.nn as nn
import torch
from pytorch.engine.CustomOps import chang_model_act

model_weight_path = r"E:\jiemi\Classification\retrieve_pytorch\models\mt1\Generation_2/best_model.pth"
imgPath = r"D:\Desktop\CLS\crack"


dataPathTxt = os.path.dirname(model_weight_path.replace("models","dataset"))
with open(f"{os.path.dirname(dataPathTxt)}/subfolder_to_id.json", 'r') as file:
    class_to_idx = json.load(file)
savePath = f"{os.path.dirname(imgPath)}/DETECT_RESULT"
if not os.path.exists(savePath):
    os.makedirs(savePath)
model = models.mobilenet_v3_large(weights=None, width_mult=0.35)
num_ftrs = model.classifier[3].in_features
model.classifier[3] = nn.Linear(num_ftrs, len(class_to_idx))
model = chang_model_act(model)

pre_weights = torch.load(model_weight_path)
pre_dict = {k: v for k, v in pre_weights.items() if
            k in model.state_dict() and v.shape == model.state_dict()[k].shape}
missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=True)
print(f"len(pre_dict): {len(pre_dict.keys())}")
print(f"missing_keys: {missing_keys}")

imgPathList = glob.glob(f"{imgPath}/*.jpg")+glob.glob(f"{imgPath}/*.png")+glob.glob(f"{imgPath}/*.bmp")
model.eval()
with torch.no_grad():
    for imgPath in tqdm(imgPathList):
        image = cv2.imread(imgPath)
        image = image[:, :, ::-1]
        img_h, img_w = image.shape[:2]
        percent = min(float(128 / img_w), float(128 / img_h))
        w = int(round(img_w * percent))
        h = int(round(img_h * percent))
        image = cv2.resize(image, (w, h))
        offset_y, offset_x = (128 - h) // 2, (128 - w) // 2
        canvas = np.zeros((128, 128, 3), dtype=image.dtype)
        canvas[offset_y:offset_y + h, offset_x:offset_x + w, :] = image
        image = canvas.astype('float32') * 1/255

        image = np.transpose(image, (2, 0, 1)).astype(np.float32)
        image = np.expand_dims(image, axis = 0)
        input_tensor = torch.from_numpy(image)
        output = model(input_tensor)
        predicted_class = torch.argmax(output, axis=1)
        savePathFold = f"{savePath}/label_{int(predicted_class)}"
        if not os.path.exists(savePathFold):
            os.makedirs(savePathFold)
        copyImgPath = f"{savePathFold}/{os.path.basename(imgPath)}"
        shutil.copy(imgPath, copyImgPath)