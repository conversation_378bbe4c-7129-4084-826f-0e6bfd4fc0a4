
import onnxruntime
import cv2
import numpy as np
from tqdm import tqdm
import os
import json


onnx_model_path = r"E:\jiemi\Classification\retrieve_pytorch\models\mt1\Generation_2/feature.onnx"
imgPathTxt = r"E:\jiemi\Classification\retrieve_pytorch\dataset\mt1/eval_data_file.txt"
with open(f"{os.path.dirname(imgPathTxt)}/subfolder_to_id.json", 'r') as file:
    class_to_idx = json.load(file)

session = onnxruntime.InferenceSession(onnx_model_path)  # 加载模型

with open(imgPathTxt, "r") as fc:
    lines = fc.read().strip().splitlines()

Batch = 48
total = 0.0
acc = 0.0
input_tensor = []
label_List = []
for line in tqdm(lines):
    imgPath, label, _ = line.split(" ")
    image = cv2.imread(imgPath)

    image = image[:, :, ::-1]
    img_h, img_w = image.shape[:2]
    percent = min(float(128 / img_w), float(128 / img_h))
    w = int(round(img_w * percent))
    h = int(round(img_h * percent))
    image = cv2.resize(image, (w, h))
    offset_y, offset_x = (128 - h) // 2, (128 - w) // 2
    canvas = np.zeros((128, 128, 3), dtype=image.dtype)
    canvas[offset_y:offset_y + h, offset_x:offset_x + w, :] = image
    image = canvas.astype('float32') * 1/255


    image = np.transpose(image, (2, 0, 1)).astype(np.float32)
    image = np.expand_dims(image, axis = 0)
    input_tensor.append(image)
    label_List.append(int(class_to_idx[label]))

    if len(input_tensor)==Batch:
        input_tensor = np.vstack(input_tensor)
        label_List = np.stack(label_List)
        input_name = session.get_inputs()[0].name
        output_name = session.get_outputs()[0].name
        output = session.run([output_name], {input_name: input_tensor})
        predicted_class = np.argmax(output[0], axis=1)
        right = predicted_class == label_List
        acc += float(np.sum(right))
        total += Batch
        input_tensor = []
        label_List = []
acc = float(acc/total)
print("acc:",acc)