import os
import sys
import torch
from pytorch.utils.general import LOGGER
from pytorch.arch.backbone.yolov5.yolo import Model
from pytorch.configs.config import get_config, config_taiho, create_attr_dict, AttrDict
from pytorch.engine.engine import ENGINE
from pytorch.engine.CustomOps import chang_model_act
import onnx
import onnxsim
from tools.creat_raw_list import creat_raw_list
import onnx_graphsurgeon as gs


if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    __dir__ = os.path.dirname(sys.executable)
    __project__ = os.path.abspath(os.path.join(__dir__, './'))
else:
    # 如果是脚本文件
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    __project__ = os.path.abspath(os.path.join(__dir__, '../'))
sys.path.append(__project__)

if __name__ == "__main__":

    material_name = "Object_Detection_2048x384_yolov5n-Relu-16_110cls"
    image_size = [384, 2048]
    detection_model_id = 0      #      0: "MobileNetV3_large_x0_35",     1: "MobileNetV3_large_x1_0"
    folder_name = "Generation_1"   # nanguazi_test  ,  nanguazi_yolov5_paste_valsets
    platform = 0
    update_mode = 0
    retain_size_info = 0
    model_dir = '{}/models/{}/{}'.format(__project__, material_name, folder_name)

    engine = ENGINE()
    engine.config = get_config(os.path.join(__project__, 'configs/config.yaml'), overrides=[])
    engine.config.profiler_options = None
    if config_taiho.is_hide(engine.config):
        engine.config['image_size'] = image_size
        engine.config = config_taiho.overwrite(engine.config)
        engine.config = AttrDict(engine.config)
        create_attr_dict(engine.config)
    engine.config['Arch']['Backbone']['name'] = "YOLOv5N_16"

    onnx_model = onnx.load(f"{model_dir}/best.onnx")

    #### 准备量化数据集 #####
    IMG_HEIGHT = image_size if isinstance(image_size, int) else image_size[0]
    IMG_WIDTH = image_size if isinstance(image_size,int) else image_size[1]
    creat_raw_list(engine.config, material_name, folder_name, dir=model_dir, IMG_WIDTH=IMG_WIDTH, IMG_HEIGHT=IMG_HEIGHT)

    if sys.platform.startswith('linux'):
        ##### 环境变量配置 #####
        os.environ["LD_LIBRARY_PATH"] = f"{__project__}/snpe/lib/x86_64-linux-clang:{os.environ.get('LD_LIBRARY_PATH', '')}"

        #### onnx 转 dlc #####
        command = f"{__project__}/snpe/snpe-onnx-to-dlc --input_network {model_dir}/best.onnx --output_path {model_dir}/feature_org.dlc"
        os.system(command)

        #### 量化 #####
        command = f"{__project__}/snpe/snpe-dlc-quant --input_dlc {model_dir}/feature_org.dlc --input_list {model_dir}/input.txt --output_dlc {model_dir}/feature_quant.dlc"
        os.system(command)

        # ##### 生成DSP离线缓存 #####
        graph = gs.import_onnx(onnx_model)
        output_tensors = ",".join([output.name for output in graph.outputs])
        command = f"{__project__}/snpe/snpe-dlc-graph-prepare --input_dlc {model_dir}/feature_quant.dlc --output_dlc {model_dir}/feature.dlc --htp_archs v68 --set_output_tensors {output_tensors}"
        os.system(command)