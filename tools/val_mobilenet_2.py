import onnxruntime
import cv2
import numpy as np
from tqdm import tqdm
import os
from tqdm import tqdm
import json
from torchvision import models
import torch.nn as nn
import torch
from pytorch.engine.CustomOps import chang_model_act
from pytorch.data.dataloader.CustomImageDataset import *
from pytorch.configs.config import update_config
import sys
from pytorch.utils.general import LOGGER

if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    __dir__ = os.path.dirname(sys.executable)
    __project__ = os.path.abspath(os.path.join(__dir__, './'))
else:
    # 如果是脚本文件
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    __project__ = os.path.abspath(os.path.join(__dir__, '../'))
sys.path.append(__project__)

if __name__ == "__main__":
    material_name = "mt1"
    image_size = [128, 128]
    detection_model_id = 0      #      0: "MobileNetV3_large_x0_35",     1: "MobileNetV3_large_x1_0"
    folder_name = "Generation_2"   # nanguazi_test  ,  nanguazi_yolov5_paste_valsets
    platform = 0
    update_mode = 0
    retain_size_info = 0

    model_weight_path = r"E:\jiemi\Classification\retrieve_pytorch\models\mt1\Generation_2/best_model.pth"
    imgPathTxt = r"E:\jiemi\Classification\retrieve_pytorch\dataset\mt1/eval_data_file.txt"
    with open(f"{os.path.dirname(imgPathTxt)}/subfolder_to_id.json", 'r') as file:
        class_to_idx = json.load(file)
    model = models.mobilenet_v3_large(weights=None, width_mult=0.35)
    num_ftrs = model.classifier[3].in_features
    model.classifier[3] = nn.Linear(num_ftrs, len(class_to_idx))

    model = chang_model_act(model)

    pre_weights = torch.load(model_weight_path)
    pre_dict = {k: v for k, v in pre_weights.items() if
                k in model.state_dict() and v.shape == model.state_dict()[k].shape}
    missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=True)
    print(f"len(pre_dict): {len(pre_dict.keys())}")
    print(f"missing_keys: {missing_keys}")

    config = update_config(__project__, material_name, folder_name, image_size, platform,
                           retain_size_info, update_mode, detection_model_id, LOGGER)

    val_dataset = CustomImageDataset(imgPathTxt,
                                         config=config,
                                         mode='valid')
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=0)

    total = 0.0
    acc = 0.0
    model.eval()
    with torch.no_grad():
        for inputs, labels in tqdm(val_loader):
            outputs = model(inputs)
            predicted_class = torch.argmax(outputs, axis=1)
            right = predicted_class == labels
            acc += float(torch.sum(right))
            total += len(labels)
            input_tensor = []
            label_List = []
    acc = float(acc/total)
    print("acc:",acc)