'''
Author:SS
描述：构建索引库或训练模型之前，构建子类别名与index的对应关系，并且对应生成data_file.txt
'''

import os
import sys
import json
import random
import re


# 自定义排序函数
def custom_sort(element):
    # 提取数字部分
    match = re.search(r'\d+', element)  # 查找连续的数字
    if match:
        num = int(match.group())  # 提取匹配的数字并转换为整数
        return num
    else:
        return element   # return float('inf')

def get_image_name(project_root, material_name, single_cls = False):

    # 获取数据集路径参数
    root_dir = os.path.join(project_root, 'dataset')

    # 获取物料名路径参数
    dataset_path = os.path.join(root_dir, material_name)

    # 检查路径是否存在
    if not os.path.exists(dataset_path) or not os.path.isdir(dataset_path):
        print("提供的路径不存在或不是一个目录")
        sys.exit(1)

    # 初始化一个空列表来存储文件名
    file_names = []

    contents = os.listdir(dataset_path)

    # 过滤出非空子文件夹
    subfolders_list = [f for f in contents if os.path.isdir(os.path.join(dataset_path, f)) and os.listdir(os.path.join(dataset_path, f))]
    subfolders_list.sort(key=custom_sort)


    # 当第一次运行本脚本时，不存在子类别名和id的映射文件，对应创建
    # 后续新增类别时，AI软件配合屏程序来修改subfolder_to_id.json文件即可，新增的类别append到json后面
    if not single_cls:
        if not os.path.exists(os.path.join(dataset_path, 'subfolder_to_id.json')):
            subfolders_dict = {subfolder: str(index) for index, subfolder in enumerate(subfolders_list)}
            json_str = json.dumps(subfolders_dict, indent=4)
            # 将JSON字符串写入文件
            with open(os.path.join(dataset_path, 'subfolder_to_id.json'), 'w') as json_file:
                json_file.write(json_str)
            print("创建子类别名与id的映射文件成功")
        # 存在映射文件时，检查json中子文件夹名称与当前文件夹是否对应一致
        else:
            with open(os.path.join(dataset_path, 'subfolder_to_id.json'), 'r') as json_file:
                subfolders_dict = json.load(json_file)
                if set(subfolders_dict.keys()) == set(subfolders_list):
                    print("子文件夹名称与JSON中值一一对应")
                else:
                    for index, subfolder_name in enumerate(subfolders_list):
                        if subfolder_name not in subfolders_dict.keys():
                            print(f"子文件夹 {subfolder_name} 与JSON中的值不匹配,加入到JSON中，更新subfolder_to_id.json！！！")
                            subfolders_dict[subfolder_name] = str(len(subfolders_dict))
                    subfolders_dict_tem = subfolders_dict
                    if set(subfolders_dict_tem.keys()) != set(subfolders_list):
                        for idx, folder_n in enumerate(list(subfolders_dict_tem.keys())):
                            if folder_n not in subfolders_list:
                                print(f"子文件夹 {folder_n} 已不存在当下路径中{dataset_path}，从JSON中删除，更新subfolder_to_id.json！！！")
                                subfolders_dict.pop(folder_n)
                        subfolders_dict = {k: str(i) for i, (k, v) in enumerate(subfolders_dict.items())}

                    json_str = json.dumps(subfolders_dict, indent=4)
                    with open(os.path.join(dataset_path, 'subfolder_to_id.json'), 'w') as json_file:
                        json_file.write(json_str)
                    print("更新子类别名与id的映射文件成功")
    else:
        subfolders_dict = {"item": "0"}
        json_str = json.dumps(subfolders_dict, indent=4)
        with open(os.path.join(dataset_path, 'subfolder_to_id.json'), 'w') as json_file:
            json_file.write(json_str)
        print("更新子类别名与id的映射文件成功")

    # 递归遍历提供的路径及其子文件夹
    for foldername, subfolders, filenames in os.walk(dataset_path):
        for filename in filenames:
            if filename.endswith('.png') or filename.endswith('.jpg') or filename.endswith('.bmp'):
                file_names.append(os.path.join(foldername, filename))

    # 将文件名写入data_file.txt/eval_data_file.txt
    # TODO:data_file.txt的操作逻辑
    val_num = int(len(file_names)*0.2) if int(len(file_names)*0.2) > 5 else 5
    val_file_names = random.sample(file_names, val_num) if len(file_names) > val_num else file_names
    train_file_names = [f for f in file_names if f not in val_file_names] if len(file_names) > val_num else file_names
    with open(os.path.join(dataset_path, 'train_data_file.txt'), 'w') as file:
        for index, file_name in enumerate(train_file_names):
            if single_cls:
                cur_sub_dir = "item"
            else:
                cur_sub_dir = os.path.basename(os.path.dirname(file_name))
            file.write(file_name + " " + cur_sub_dir + " " + str(index)+'\n')
    with open(os.path.join(dataset_path, 'eval_data_file.txt'), 'w') as file:
        for index, file_name in enumerate(val_file_names):
            if single_cls:
                cur_sub_dir = "item"
            else:
                cur_sub_dir = os.path.basename(os.path.dirname(file_name))
            file.write(file_name + " " + cur_sub_dir + " " + str(index)+'\n')
    with open(os.path.join(dataset_path, 'data_file.txt'), 'w') as file:
        for index, file_name in enumerate(file_names):
            if single_cls:
                cur_sub_dir = "item"
            else:
                cur_sub_dir = os.path.basename(os.path.dirname(file_name))
            file.write(file_name + " " + cur_sub_dir + " " + str(index)+'\n')

    print("文件名已保存在train_data_file.txt/eval_data_file.txt/data_file.txt中")

if __name__ == "__main__":

    # 检查是否提供了路径参数
    if len(sys.argv) != 3:
        print("请提供一个路径参数")
        sys.exit(1)

    project_root = sys.argv[1]
    material_name = sys.argv[2]

    get_image_name(project_root, material_name)