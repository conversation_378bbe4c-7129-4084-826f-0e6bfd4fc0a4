
import os
import json
import shutil
from tqdm import tqdm
import torch
from PIL import Image
from torchvision import transforms
import matplotlib.pyplot as plt
import cv2 as cv
import time
from torchvision import datasets, transforms, models
import sys
import torch.nn as nn
from pytorch.data.dataloader.CustomImageDataset import *
from pytorch.configs.config import update_config
FOLDER_DICT = {0:"Generation_1", 1:"Generation_2"}


if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    __dir__ = os.path.dirname(sys.executable)
    __project__ = os.path.abspath(os.path.join(__dir__, './'))
else:
    # 如果是脚本文件
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    __project__ = os.path.abspath(os.path.join(__dir__, '../'))
sys.path.append(__project__)


def main(test_path, material_name, platform, update_mode,
         infer_batch, image_width, image_height, model_arch, retain_size_info, MutilLabel=False):
    save_path = f"{test_path}_predict"
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    image_size = [int(image_height), int(image_width)]
    folder_name = FOLDER_DICT[update_mode]

    config = update_config(__project__, material_name, folder_name, image_size, platform,
                           retain_size_info, update_mode, 0)

    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    # data_transform = transforms.Compose([transforms.RandomHorizontalFlip() ,transforms.ToTensor()])
    data_transform = create_operators(config['DataLoader']['Eval']['dataset']['transform_ops'])

    # read class_indict
    image_root = os.path.join(os.path.join(__project__, 'dataset'), material_name)
    json_path = f"{image_root}/subfolder_to_id.json"
    assert os.path.exists(json_path), "file: '{}' dose not exist.".format(json_path)
    json_file = open(json_path, "r")
    class_indict = json.load(json_file)
    class_indict = {int(value):key   for key,value in class_indict.items()}

    # create model
    model = models.mobilenet_v3_large(weights=None, width_mult=0.35)
    num_ftrs = model.classifier[3].in_features
    model.classifier[3] = nn.Linear(num_ftrs, len(class_indict))  # 替换最后的分类层，使其适应你的类别数
    # load model weights
    model_weight_root = os.path.join(os.path.join(__project__, 'models'), material_name)
    model_weight_path = f"{model_weight_root}/Generation_2/best_model.pth"
    if os.path.exists(model_weight_path):
        pre_weights = torch.load(model_weight_path)
        pre_dict = {k: v for k, v in pre_weights.items() if
                    k in model.state_dict() and v.shape == model.state_dict()[k].shape}
        missing_keys, unexpected_keys = model.load_state_dict(pre_dict, strict=False)
        print(f"len(pre_dict): {len(pre_dict.keys())}")
        print(f"missing_keys: {missing_keys}")

    model = model.to(device)
    model.eval()
    with torch.no_grad():
        List = os.listdir(test_path)
        test_Num = len(List)
        img_list = []
        probs_group = []
        classes_group = []
        for id, item in enumerate(tqdm(List, total = len(List))):
            img_path = os.path.join(os.path.abspath(test_path), item)
            assert os.path.exists(img_path), "file: '{}' dose not exist.".format(img_path)
            img = Image.open(img_path)
            for op in data_transform:
                img = op(img)
            img = torch.from_numpy(img)
            output = model(img[None].to(device)).cpu()  # predict class
            if MutilLabel:
                predict = output.sigmoid()
                i, j = (predict > 0.3).nonzero(as_tuple=False).T
                probs, classes = predict[i, j], j
            else:
                predict = torch.softmax(output, dim=1)
                probs, classes = torch.max(predict, dim=1)
            for idx, (pro, cls_id) in enumerate(zip(probs, classes)):
                cls_name = class_indict[int(cls_id)]
                clsFolderPath = os.path.join(save_path, cls_name)
                if not os.path.exists(clsFolderPath):
                    os.mkdir(clsFolderPath)
                dstimgPath = f"{clsFolderPath}/{str(id)}_{str(round(float(pro),4))}.png"
                shutil.copy(img_path, dstimgPath)


if __name__ == '__main__':

    test_path = r"D:\Desktop\cy1"
    main(test_path, material_name="Seed_2K_D", platform=0, update_mode=1,
         infer_batch=1, image_width=128, image_height=128, model_arch=0, retain_size_info=0)
